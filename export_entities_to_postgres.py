#!/usr/bin/env python3
"""
将GraphRAG提取的实体数据导出到PostgreSQL数据库
"""

import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import json
import os
from typing import Optional
import argparse
from datetime import datetime

class EntityExporter:
    def __init__(self, db_config: dict):
        """
        初始化数据库连接
        
        Args:
            db_config: 数据库配置字典，包含host, port, database, user, password
        """
        self.db_config = db_config
        self.conn = None
        self.cursor = None
    
    def connect(self):
        """连接到PostgreSQL数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.cursor = self.conn.cursor()
            print(f"✅ 成功连接到PostgreSQL数据库: {self.db_config['database']}")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("✅ 数据库连接已关闭")
    
    def create_entities_table(self, table_name: str = "graphrag_entities", drop_if_exists: bool = False):
        """
        创建实体表
        
        Args:
            table_name: 表名
            drop_if_exists: 如果表存在是否删除重建
        """
        try:
            if drop_if_exists:
                drop_sql = f"DROP TABLE IF EXISTS {table_name};"
                self.cursor.execute(drop_sql)
                print(f"🗑️  已删除现有表: {table_name}")
            
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                id VARCHAR(255) PRIMARY KEY,
                human_readable_id INTEGER,
                title VARCHAR(500) NOT NULL,
                type VARCHAR(100),
                description TEXT,
                text_unit_ids TEXT[],  -- 存储为文本数组
                frequency INTEGER,
                degree INTEGER,
                x FLOAT,
                y FLOAT,
                temporal_info TEXT,
                spatial_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            self.cursor.execute(create_sql)
            
            # 创建索引以提高查询性能
            indexes = [
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_title ON {table_name}(title);",
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_type ON {table_name}(type);",
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_temporal ON {table_name}(temporal_info);",
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_spatial ON {table_name}(spatial_info);",
            ]
            
            for index_sql in indexes:
                self.cursor.execute(index_sql)
            
            self.conn.commit()
            print(f"✅ 成功创建表: {table_name}")
            
        except Exception as e:
            self.conn.rollback()
            print(f"❌ 创建表失败: {e}")
            raise
    
    def load_entities_from_parquet(self, parquet_path: str) -> pd.DataFrame:
        """
        从parquet文件加载实体数据
        
        Args:
            parquet_path: parquet文件路径
            
        Returns:
            pandas DataFrame
        """
        if not os.path.exists(parquet_path):
            raise FileNotFoundError(f"找不到文件: {parquet_path}")
        
        df = pd.read_parquet(parquet_path)
        print(f"📊 从 {parquet_path} 加载了 {len(df)} 个实体")
        print(f"📋 列名: {df.columns.tolist()}")
        
        return df
    
    def prepare_data_for_insert(self, df: pd.DataFrame) -> list:
        """
        准备插入数据库的数据
        
        Args:
            df: 实体DataFrame
            
        Returns:
            准备好的数据列表
        """
        data = []
        
        for _, row in df.iterrows():
            # 处理text_unit_ids - 转换为PostgreSQL数组格式
            text_unit_ids = row.get('text_unit_ids', [])
            if isinstance(text_unit_ids, list):
                text_unit_ids_array = text_unit_ids
            else:
                # 如果是字符串，尝试解析
                try:
                    text_unit_ids_array = json.loads(text_unit_ids) if text_unit_ids else []
                except:
                    text_unit_ids_array = [str(text_unit_ids)] if text_unit_ids else []
            
            # 处理NaN值
            def handle_nan(value):
                if pd.isna(value):
                    return None
                return value
            
            record = (
                str(row['id']),
                int(row['human_readable_id']) if pd.notna(row['human_readable_id']) else None,
                str(row['title']),
                handle_nan(row.get('type')),
                handle_nan(row.get('description')),
                text_unit_ids_array,
                int(row['frequency']) if pd.notna(row.get('frequency')) else None,
                int(row['degree']) if pd.notna(row.get('degree')) else None,
                float(row['x']) if pd.notna(row.get('x')) else None,
                float(row['y']) if pd.notna(row.get('y')) else None,
                handle_nan(row.get('temporal_info')),
                handle_nan(row.get('spatial_info')),
                datetime.now(),  # created_at
                datetime.now()   # updated_at
            )
            data.append(record)
        
        return data
    
    def insert_entities(self, data: list, table_name: str = "graphrag_entities"):
        """
        批量插入实体数据
        
        Args:
            data: 准备好的数据列表
            table_name: 表名
        """
        try:
            insert_sql = f"""
            INSERT INTO {table_name} (
                id, human_readable_id, title, type, description, 
                text_unit_ids, frequency, degree, x, y, 
                temporal_info, spatial_info, created_at, updated_at
            ) VALUES %s
            ON CONFLICT (id) DO UPDATE SET
                human_readable_id = EXCLUDED.human_readable_id,
                title = EXCLUDED.title,
                type = EXCLUDED.type,
                description = EXCLUDED.description,
                text_unit_ids = EXCLUDED.text_unit_ids,
                frequency = EXCLUDED.frequency,
                degree = EXCLUDED.degree,
                x = EXCLUDED.x,
                y = EXCLUDED.y,
                temporal_info = EXCLUDED.temporal_info,
                spatial_info = EXCLUDED.spatial_info,
                updated_at = EXCLUDED.updated_at;
            """
            
            execute_values(
                self.cursor,
                insert_sql,
                data,
                template=None,
                page_size=100
            )
            
            self.conn.commit()
            print(f"✅ 成功插入/更新 {len(data)} 条实体记录")
            
        except Exception as e:
            self.conn.rollback()
            print(f"❌ 插入数据失败: {e}")
            raise
    
    def get_statistics(self, table_name: str = "graphrag_entities"):
        """
        获取表统计信息
        
        Args:
            table_name: 表名
        """
        try:
            # 总数统计
            self.cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            total_count = self.cursor.fetchone()[0]
            
            # 按类型统计
            self.cursor.execute(f"""
                SELECT type, COUNT(*) 
                FROM {table_name} 
                WHERE type IS NOT NULL 
                GROUP BY type 
                ORDER BY COUNT(*) DESC;
            """)
            type_stats = self.cursor.fetchall()
            
            # 时空信息统计
            self.cursor.execute(f"""
                SELECT 
                    COUNT(CASE WHEN temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN' THEN 1 END) as with_temporal,
                    COUNT(CASE WHEN spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN' THEN 1 END) as with_spatial,
                    COUNT(CASE WHEN temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN' 
                                AND spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN' THEN 1 END) as with_both
                FROM {table_name};
            """)
            spatiotemporal_stats = self.cursor.fetchone()
            
            print(f"\n📊 数据库表 {table_name} 统计信息:")
            print(f"   总实体数量: {total_count}")
            print(f"   有时间信息: {spatiotemporal_stats[0]}")
            print(f"   有空间信息: {spatiotemporal_stats[1]}")
            print(f"   有完整时空信息: {spatiotemporal_stats[2]}")
            
            print(f"\n📈 按类型分布:")
            for entity_type, count in type_stats:
                print(f"   {entity_type}: {count}")
                
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="将GraphRAG实体导出到PostgreSQL")
    parser.add_argument("--parquet-path", default="ragtest/output/entities.parquet", 
                       help="entities.parquet文件路径")
    parser.add_argument("--host", default="localhost", help="PostgreSQL主机")
    parser.add_argument("--port", default="5432", help="PostgreSQL端口")
    parser.add_argument("--database", default="graphrag", help="数据库名")
    parser.add_argument("--user", default="postgres", help="用户名")
    parser.add_argument("--password", help="密码")
    parser.add_argument("--table", default="graphrag_entities", help="表名")
    parser.add_argument("--drop-table", action="store_true", help="删除现有表重新创建")
    
    args = parser.parse_args()
    
    # 如果没有提供密码，提示输入
    if not args.password:
        import getpass
        args.password = getpass.getpass("请输入PostgreSQL密码: ")
    
    # 数据库配置
    db_config = {
        'host': args.host,
        'port': args.port,
        'database': args.database,
        'user': args.user,
        'password': args.password
    }
    
    exporter = EntityExporter(db_config)
    
    try:
        # 连接数据库
        exporter.connect()
        
        # 创建表
        exporter.create_entities_table(args.table, args.drop_table)
        
        # 加载数据
        df = exporter.load_entities_from_parquet(args.parquet_path)
        
        # 准备数据
        data = exporter.prepare_data_for_insert(df)
        
        # 插入数据
        exporter.insert_entities(data, args.table)
        
        # 显示统计信息
        exporter.get_statistics(args.table)
        
        print(f"\n🎉 成功将GraphRAG实体导出到PostgreSQL表: {args.table}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
    finally:
        exporter.disconnect()

if __name__ == "__main__":
    main()
