{
  "search.exclude": {
    "**/.yarn": true,
    "**/.pnp.*": true
  },
  "editor.formatOnSave": false,
  "eslint.nodePath": ".yarn/sdks",
  "typescript.tsdk": ".yarn/sdks/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "javascript.preferences.importModuleSpecifier": "relative",
  "javascript.preferences.importModuleSpecifierEnding": "js",
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.importModuleSpecifierEnding": "js",
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.ts, ${capture}.hooks.ts, ${capture}.hooks.tsx, ${capture}.contexts.ts, ${capture}.stories.tsx, ${capture}.story.tsx, ${capture}.spec.tsx, ${capture}.base.ts, ${capture}.base.tsx, ${capture}.types.ts, ${capture}.styles.ts, ${capture}.styles.tsx, ${capture}.utils.ts, ${capture}.utils.tsx, ${capture}.constants.ts, ${capture}.module.scss, ${capture}.module.css, ${capture}.md",
    "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
    "*.jsx": "${capture}.js",
    "*.tsx": "${capture}.ts, ${capture}.hooks.ts, ${capture}.hooks.tsx, ${capture}.contexts.ts, ${capture}.stories.tsx, ${capture}.story.tsx, ${capture}.spec.tsx, ${capture}.base.ts, ${capture}.base.tsx, ${capture}.types.ts, ${capture}.styles.ts, ${capture}.styles.tsx, ${capture}.utils.ts, ${capture}.utils.tsx, ${capture}.constants.ts, ${capture}.module.scss, ${capture}.module.css, ${capture}.md, ${capture}.css",
    "tsconfig.json": "tsconfig.*.json",
    "package.json": "package-lock.json, turbo.json, tsconfig.json, rome.json, biome.json, .npmignore, dictionary.txt, cspell.config.yaml",
    "README.md": "*.md, LICENSE, CODEOWNERS",
    ".eslintrc": ".eslintignore",
    ".prettierrc": ".prettierignore",
    ".gitattributes": ".gitignore",
    ".yarnrc.yml": "yarn.lock, .pnp.*",
    "jest.config.js": "jest.setup.mjs",
    "pyproject.toml": "poetry.lock, poetry.toml, mkdocs.yaml",
    "cspell.config.yaml": "dictionary.txt"
  },
  "azureFunctions.postDeployTask": "npm install (functions)",
  "azureFunctions.projectLanguage": "TypeScript",
  "azureFunctions.projectRuntime": "~4",
  "debug.internalConsoleOptions": "neverOpen",
  "azureFunctions.preDeployTask": "npm prune (functions)",
  "appService.zipIgnorePattern": [
    "node_modules{,/**}",
    ".vscode{,/**}"
  ],
  "python.defaultInterpreterPath": "python/services/.venv/bin/python",
  "python.languageServer": "Pylance",
  "cSpell.customDictionaries": {
    "project-words": {
      "name": "project-words",
      "path": "${workspaceRoot}/dictionary.txt",
      "description": "Words used in this project",
      "addWords": true
    },
    "custom": true, // Enable the `custom` dictionary
    "internal-terms": true // Disable the `internal-terms` dictionary
  }
}
