{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Copyright (c) 2024 Microsoft Corporation.\n", "# Licensed under the MIT License."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import pandas as pd\n", "import tiktoken\n", "\n", "from graphrag.query.context_builder.entity_extraction import EntityVectorStoreKey\n", "from graphrag.query.indexer_adapters import (\n", "    read_indexer_covariates,\n", "    read_indexer_entities,\n", "    read_indexer_relationships,\n", "    read_indexer_reports,\n", "    read_indexer_text_units,\n", ")\n", "from graphrag.query.question_gen.local_gen import LocalQuestionGen\n", "from graphrag.query.structured_search.local_search.mixed_context import (\n", "    LocalSearchMixedContext,\n", ")\n", "from graphrag.query.structured_search.local_search.search import LocalSearch\n", "from graphrag.vector_stores.lancedb import LanceDBVectorStore"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Local Search Example\n", "\n", "Local search method generates answers by combining relevant data from the AI-extracted knowledge-graph with text chunks of the raw documents. This method is suitable for questions that require an understanding of specific entities mentioned in the documents (e.g. What are the healing properties of chamomile?)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load text units and graph data tables as context for local search\n", "\n", "- In this test we first load indexing outputs from parquet files to dataframes, then convert these dataframes into collections of data objects aligning with the knowledge model."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load tables to dataframes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INPUT_DIR = \"./inputs/operation dulce\"\n", "LANCEDB_URI = f\"{INPUT_DIR}/lancedb\"\n", "\n", "COMMUNITY_REPORT_TABLE = \"community_reports\"\n", "ENTITY_TABLE = \"entities\"\n", "COMMUNITY_TABLE = \"communities\"\n", "RELATIONSHIP_TABLE = \"relationships\"\n", "COVARIATE_TABLE = \"covariates\"\n", "TEXT_UNIT_TABLE = \"text_units\"\n", "COMMUNITY_LEVEL = 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Read entities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# read nodes table to get community and degree data\n", "entity_df = pd.read_parquet(f\"{INPUT_DIR}/{ENTITY_TABLE}.parquet\")\n", "community_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_TABLE}.parquet\")\n", "\n", "entities = read_indexer_entities(entity_df, community_df, COMMUNITY_LEVEL)\n", "\n", "# load description embeddings to an in-memory lancedb vectorstore\n", "# to connect to a remote db, specify url and port values.\n", "description_embedding_store = LanceDBVectorStore(\n", "    collection_name=\"default-entity-description\",\n", ")\n", "description_embedding_store.connect(db_uri=LANCEDB_URI)\n", "\n", "print(f\"Entity count: {len(entity_df)}\")\n", "entity_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Read relationships"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["relationship_df = pd.read_parquet(f\"{INPUT_DIR}/{RELATIONSHIP_TABLE}.parquet\")\n", "relationships = read_indexer_relationships(relationship_df)\n", "\n", "print(f\"Relationship count: {len(relationship_df)}\")\n", "relationship_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NOTE: covariates are turned off by default, because they generally need prompt tuning to be valuable\n", "# Please see the GRAPHRAG_CLAIM_* settings\n", "covariate_df = pd.read_parquet(f\"{INPUT_DIR}/{COVARIATE_TABLE}.parquet\")\n", "\n", "claims = read_indexer_covariates(covariate_df)\n", "\n", "print(f\"Claim records: {len(claims)}\")\n", "covariates = {\"claims\": claims}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Read community reports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report_df = pd.read_parquet(f\"{INPUT_DIR}/{COMMUNITY_REPORT_TABLE}.parquet\")\n", "reports = read_indexer_reports(report_df, community_df, COMMUNITY_LEVEL)\n", "\n", "print(f\"Report records: {len(report_df)}\")\n", "report_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Read text units"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text_unit_df = pd.read_parquet(f\"{INPUT_DIR}/{TEXT_UNIT_TABLE}.parquet\")\n", "text_units = read_indexer_text_units(text_unit_df)\n", "\n", "print(f\"Text unit records: {len(text_unit_df)}\")\n", "text_unit_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from graphrag.config.enums import ModelType\n", "from graphrag.config.models.language_model_config import LanguageModelConfig\n", "from graphrag.language_model.manager import ModelManager\n", "\n", "api_key = os.environ[\"GRAPHRAG_API_KEY\"]\n", "llm_model = os.environ[\"GRAPHRAG_LLM_MODEL\"]\n", "embedding_model = os.environ[\"GRAPHRAG_EMBEDDING_MODEL\"]\n", "\n", "chat_config = LanguageModelConfig(\n", "    api_key=api_key,\n", "    type=ModelType.OpenAIChat,\n", "    model=llm_model,\n", "    max_retries=20,\n", ")\n", "chat_model = ModelManager().get_or_create_chat_model(\n", "    name=\"local_search\",\n", "    model_type=ModelType.OpenAIChat,\n", "    config=chat_config,\n", ")\n", "\n", "token_encoder = tiktoken.encoding_for_model(llm_model)\n", "\n", "embedding_config = LanguageModelConfig(\n", "    api_key=api_key,\n", "    type=ModelType.OpenAIEmbedding,\n", "    model=embedding_model,\n", "    max_retries=20,\n", ")\n", "\n", "text_embedder = ModelManager().get_or_create_embedding_model(\n", "    name=\"local_search_embedding\",\n", "    model_type=ModelType.OpenAIEmbedding,\n", "    config=embedding_config,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create local search context builder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["context_builder = LocalSearchMixedContext(\n", "    community_reports=reports,\n", "    text_units=text_units,\n", "    entities=entities,\n", "    relationships=relationships,\n", "    # if you did not run covariates during indexing, set this to None\n", "    covariates=covariates,\n", "    entity_text_embeddings=description_embedding_store,\n", "    embedding_vectorstore_key=EntityVectorStoreKey.ID,  # if the vectorstore uses entity title as ids, set this to EntityVectorStoreKey.TITLE\n", "    text_embedder=text_embedder,\n", "    token_encoder=token_encoder,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create local search engine"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# text_unit_prop: proportion of context window dedicated to related text units\n", "# community_prop: proportion of context window dedicated to community reports.\n", "# The remaining proportion is dedicated to entities and relationships. Sum of text_unit_prop and community_prop should be <= 1\n", "# conversation_history_max_turns: maximum number of turns to include in the conversation history.\n", "# conversation_history_user_turns_only: if True, only include user queries in the conversation history.\n", "# top_k_mapped_entities: number of related entities to retrieve from the entity description embedding store.\n", "# top_k_relationships: control the number of out-of-network relationships to pull into the context window.\n", "# include_entity_rank: if True, include the entity rank in the entity table in the context window. Default entity rank = node degree.\n", "# include_relationship_weight: if True, include the relationship weight in the context window.\n", "# include_community_rank: if True, include the community rank in the context window.\n", "# return_candidate_context: if True, return a set of dataframes containing all candidate entity/relationship/covariate records that\n", "# could be relevant. Note that not all of these records will be included in the context window. The \"in_context\" column in these\n", "# dataframes indicates whether the record is included in the context window.\n", "# max_tokens: maximum number of tokens to use for the context window.\n", "\n", "\n", "local_context_params = {\n", "    \"text_unit_prop\": 0.5,\n", "    \"community_prop\": 0.1,\n", "    \"conversation_history_max_turns\": 5,\n", "    \"conversation_history_user_turns_only\": True,\n", "    \"top_k_mapped_entities\": 10,\n", "    \"top_k_relationships\": 10,\n", "    \"include_entity_rank\": True,\n", "    \"include_relationship_weight\": True,\n", "    \"include_community_rank\": <PERSON><PERSON><PERSON>,\n", "    \"return_candidate_context\": <PERSON><PERSON><PERSON>,\n", "    \"embedding_vectorstore_key\": EntityVectorStoreKey.ID,  # set this to EntityVectorStoreKey.TITLE if the vectorstore uses entity title as ids\n", "    \"max_tokens\": 12_000,  # change this based on the token limit you have on your model (if you are using a model with 8k limit, a good setting could be 5000)\n", "}\n", "\n", "model_params = {\n", "    \"max_tokens\": 2_000,  # change this based on the token limit you have on your model (if you are using a model with 8k limit, a good setting could be 1000=1500)\n", "    \"temperature\": 0.0,\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["search_engine = LocalSearch(\n", "    model=chat_model,\n", "    context_builder=context_builder,\n", "    token_encoder=token_encoder,\n", "    model_params=model_params,\n", "    context_builder_params=local_context_params,\n", "    response_type=\"multiple paragraphs\",  # free form text describing the response type and format, can be anything, e.g. prioritized list, single paragraph, multiple paragraphs, multiple-page report\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Run local search on sample queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = await search_engine.search(\"Tell me about Agent <PERSON>\")\n", "print(result.response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["question = \"Tell me about <PERSON><PERSON>\"\n", "result = await search_engine.search(question)\n", "print(result.response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Inspecting the context data used to generate the response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.context_data[\"entities\"].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.context_data[\"relationships\"].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if \"reports\" in result.context_data:\n", "    result.context_data[\"reports\"].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result.context_data[\"sources\"].head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if \"claims\" in result.context_data:\n", "    print(result.context_data[\"claims\"].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Question Generation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This function takes a list of user queries and generates the next candidate questions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["question_generator = LocalQuestionGen(\n", "    model=chat_model,\n", "    context_builder=context_builder,\n", "    token_encoder=token_encoder,\n", "    model_params=model_params,\n", "    context_builder_params=local_context_params,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["question_history = [\n", "    \"Tell me about <PERSON>\",\n", "    \"What happens in Dulce military base?\",\n", "]\n", "candidate_questions = await question_generator.agenerate(\n", "    question_history=question_history, context_data=None, question_count=5\n", ")\n", "print(candidate_questions.response)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}