<!--
Thanks for contributing to GraphRAG!

Please do not make *Draft* pull requests, as they still notify anyone watching the repo.

Create a pull request when it is ready for review and feedback.

About this template

The following template aims to help contributors write a good description for their pull requests.
We'd like you to provide a description of the changes in your pull request (i.e. bugs fixed or features added), the motivation behind the changes, and complete the checklist below before opening a pull request.

Feel free to discard it if you need to (e.g. when you just fix a typo). -->

## Description

[Provide a brief description of the changes made in this pull request.]

## Related Issues

[Reference any related issues or tasks that this pull request addresses.]

## Proposed Changes

[List the specific changes made in this pull request.]

## Checklist

- [ ] I have tested these changes locally.
- [ ] I have reviewed the code changes.
- [ ] I have updated the documentation (if necessary).
- [ ] I have added appropriate unit tests (if applicable).

## Additional Notes

[Add any additional notes or context that may be helpful for the reviewer(s).]
