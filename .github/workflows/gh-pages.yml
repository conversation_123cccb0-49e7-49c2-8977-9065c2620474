name: gh-pages
on:
  push:
    branches: [main]
permissions:
  contents: write

env:
  POETRY_VERSION: '1.8.3'
  PYTHON_VERSION: '3.11'

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      GH_PAGES: 1
      DEBUG: 1
      GRAPHRAG_API_KEY: ${{ secrets.GRAPHRAG_API_KEY }}
      GRAPHRAG_LLM_MODEL: ${{ secrets.GRAPHRAG_LLM_MODEL }}
      GRAPHRAG_EMBEDDING_MODEL: ${{ secrets.GRAPHRAG_EMBEDDING_MODEL }}

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Poetry ${{ env.POETRY_VERSION }}
        uses: abatilo/actions-poetry@v3.0.0
        with:
          poetry-version: ${{ env.POETRY_VERSION }}

      - name: poetry intsall
        shell: bash
        run: poetry install
  
      - name: mkdocs build
        shell: bash
        run: poetry run poe build_docs

      - name: List Docsite Contents
        run: find site

      - name: Deploy to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4.6.4
        with:
          branch: gh-pages
          folder: site
          clean: true
