#!/usr/bin/env python3
"""
测试PostgreSQL数据库连接
"""

import psycopg2
import getpass

def test_connection():
    """测试数据库连接"""
    
    print("🔧 PostgreSQL连接测试")
    print("=" * 40)
    
    # 获取连接信息
    host = input("主机地址 (默认: localhost): ").strip() or 'localhost'
    port = input("端口 (默认: 5432): ").strip() or '5432'
    database = input("数据库名 (默认: postgres): ").strip() or 'postgres'
    user = input("用户名 (默认: postgres): ").strip() or 'postgres'
    password = getpass.getpass("密码: ")
    
    try:
        print("\n📡 正在连接数据库...")
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password
        )
        
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        
        print("✅ 连接成功！")
        print(f"📊 PostgreSQL版本: {version}")
        
        # 检查是否存在graphrag数据库
        cursor.execute("SELECT datname FROM pg_database WHERE datname = 'graphrag';")
        graphrag_db = cursor.fetchone()
        
        if graphrag_db:
            print("✅ 发现 'graphrag' 数据库")
        else:
            print("⚠️  未发现 'graphrag' 数据库")
            create_db = input("是否创建 'graphrag' 数据库? (y/N): ").strip().lower()
            if create_db == 'y':
                try:
                    # 注意：不能在事务中创建数据库
                    conn.autocommit = True
                    cursor.execute("CREATE DATABASE graphrag;")
                    print("✅ 成功创建 'graphrag' 数据库")
                except Exception as e:
                    print(f"❌ 创建数据库失败: {e}")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 数据库连接测试完成！")
        print("💡 你现在可以运行 python quick_export.py 来导入实体数据")
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n💡 请检查:")
        print("   1. PostgreSQL服务是否正在运行")
        print("   2. 连接信息是否正确")
        print("   3. 用户是否有相应权限")

if __name__ == "__main__":
    test_connection()
