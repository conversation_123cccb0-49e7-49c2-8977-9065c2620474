-- GraphRAG实体数据查询示例
-- 请根据你的实际表名调整查询语句

-- 1. 基本查询 - 查看所有实体
SELECT * FROM graphrag_entities LIMIT 10;

-- 2. 按实体类型统计
SELECT 
    type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM graphrag_entities 
WHERE type IS NOT NULL
GROUP BY type 
ORDER BY count DESC;

-- 3. 查看有时间信息的实体
SELECT title, type, temporal_info, description
FROM graphrag_entities 
WHERE temporal_info IS NOT NULL 
  AND temporal_info != 'UNKNOWN'
ORDER BY title;

-- 4. 查看有空间信息的实体
SELECT title, type, spatial_info, description
FROM graphrag_entities 
WHERE spatial_info IS NOT NULL 
  AND spatial_info != 'UNKNOWN'
ORDER BY title;

-- 5. 查看同时有时空信息的实体
SELECT title, type, temporal_info, spatial_info, description
FROM graphrag_entities 
WHERE temporal_info IS NOT NULL 
  AND temporal_info != 'UNKNOWN'
  AND spatial_info IS NOT NULL 
  AND spatial_info != 'UNKNOWN'
ORDER BY title;

-- 6. 按度数(连接数)排序的重要实体
SELECT title, type, degree, temporal_info, spatial_info
FROM graphrag_entities 
WHERE degree IS NOT NULL
ORDER BY degree DESC
LIMIT 20;

-- 7. 按频率排序的常见实体
SELECT title, type, frequency, temporal_info, spatial_info
FROM graphrag_entities 
WHERE frequency IS NOT NULL
ORDER BY frequency DESC
LIMIT 20;

-- 8. 搜索特定实体
SELECT title, type, temporal_info, spatial_info, description
FROM graphrag_entities 
WHERE title ILIKE '%einstein%'  -- 不区分大小写搜索
   OR description ILIKE '%einstein%';

-- 9. 时间范围查询（假设时间信息包含年份）
SELECT title, type, temporal_info, spatial_info
FROM graphrag_entities 
WHERE temporal_info ~ '\d{4}'  -- 包含4位数字（年份）
  AND temporal_info != 'UNKNOWN'
ORDER BY temporal_info;

-- 10. 地理位置查询
SELECT title, type, temporal_info, spatial_info
FROM graphrag_entities 
WHERE spatial_info ILIKE '%germany%'
   OR spatial_info ILIKE '%switzerland%'
   OR spatial_info ILIKE '%america%'
ORDER BY spatial_info;

-- 11. 复合查询 - 特定时期和地点的实体
SELECT title, type, temporal_info, spatial_info, description
FROM graphrag_entities 
WHERE temporal_info LIKE '%19%'  -- 19世纪或20世纪
  AND spatial_info ILIKE '%germany%'
ORDER BY temporal_info;

-- 12. 统计信息汇总
SELECT 
    COUNT(*) as total_entities,
    COUNT(CASE WHEN temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN' THEN 1 END) as with_temporal,
    COUNT(CASE WHEN spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN' THEN 1 END) as with_spatial,
    COUNT(CASE WHEN temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN' 
                AND spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN' THEN 1 END) as with_both,
    ROUND(AVG(degree), 2) as avg_degree,
    ROUND(AVG(frequency), 2) as avg_frequency
FROM graphrag_entities;

-- 13. 查找描述最长的实体（可能包含更多信息）
SELECT title, type, temporal_info, spatial_info, LENGTH(description) as desc_length
FROM graphrag_entities 
WHERE description IS NOT NULL
ORDER BY LENGTH(description) DESC
LIMIT 10;

-- 14. 查找连接最多的实体（图中的关键节点）
SELECT title, type, degree, temporal_info, spatial_info
FROM graphrag_entities 
WHERE degree IS NOT NULL
  AND degree > (SELECT AVG(degree) FROM graphrag_entities WHERE degree IS NOT NULL)
ORDER BY degree DESC;

-- 15. 文本单元关联分析
SELECT 
    title, 
    type, 
    array_length(text_unit_ids, 1) as text_unit_count,
    temporal_info,
    spatial_info
FROM graphrag_entities 
WHERE text_unit_ids IS NOT NULL
ORDER BY array_length(text_unit_ids, 1) DESC
LIMIT 10;

-- 16. 创建视图 - 完整时空信息的实体
CREATE OR REPLACE VIEW entities_with_spatiotemporal AS
SELECT 
    title,
    type,
    temporal_info,
    spatial_info,
    description,
    degree,
    frequency,
    created_at
FROM graphrag_entities 
WHERE temporal_info IS NOT NULL 
  AND temporal_info != 'UNKNOWN'
  AND spatial_info IS NOT NULL 
  AND spatial_info != 'UNKNOWN';

-- 17. 查询视图
SELECT * FROM entities_with_spatiotemporal ORDER BY title;

-- 18. 全文搜索（如果需要更复杂的搜索）
-- 首先创建全文搜索索引
CREATE INDEX IF NOT EXISTS idx_entities_fulltext 
ON graphrag_entities USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- 全文搜索查询
SELECT title, type, temporal_info, spatial_info,
       ts_rank(to_tsvector('english', title || ' ' || COALESCE(description, '')), 
               plainto_tsquery('english', 'physics theory')) as rank
FROM graphrag_entities 
WHERE to_tsvector('english', title || ' ' || COALESCE(description, '')) 
      @@ plainto_tsquery('english', 'physics theory')
ORDER BY rank DESC;

-- 19. 导出数据到CSV（在psql中使用）
-- \copy (SELECT title, type, temporal_info, spatial_info FROM graphrag_entities) TO 'entities_export.csv' WITH CSV HEADER;

-- 20. 数据质量检查
SELECT 
    'Missing temporal info' as issue,
    COUNT(*) as count
FROM graphrag_entities 
WHERE temporal_info IS NULL OR temporal_info = 'UNKNOWN'

UNION ALL

SELECT 
    'Missing spatial info' as issue,
    COUNT(*) as count
FROM graphrag_entities 
WHERE spatial_info IS NULL OR spatial_info = 'UNKNOWN'

UNION ALL

SELECT 
    'Missing description' as issue,
    COUNT(*) as count
FROM graphrag_entities 
WHERE description IS NULL OR description = ''

UNION ALL

SELECT 
    'Missing type' as issue,
    COUNT(*) as count
FROM graphrag_entities 
WHERE type IS NULL;
