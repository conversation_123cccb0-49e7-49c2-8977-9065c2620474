name: GraphRAG CI
pool:
  vmImage: ubuntu-latest

trigger:
  batch: true
  branches:
    include:
      - main

variables:
  isMain: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  pythonVersion: "3.10"
  poetryVersion: "1.6.1"
  nodeVersion: "18.x"
  artifactsFullFeedName: "Resilience/resilience_python"

stages:
  - stage: Compliance
    dependsOn: []
    jobs:
      - job: compliance
        displayName: Compliance
        pool:
          vmImage: windows-latest
        steps:
          - task: CredScan@3
            inputs:
              outputFormat: sarif
              debugMode: false

          - task: ComponentGovernanceComponentDetection@0
            inputs:
              scanType: "Register"
              verbosity: "Verbose"
              alertWarningLevel: "High"

          - task: PublishSecurityAnalysisLogs@3
            inputs:
              ArtifactName: "CodeAnalysisLogs"
              ArtifactType: "Container"