{"changes": [{"description": "Remove Dynamic Max Retries support. Refactor typer typing in cli interface", "type": "minor"}, {"description": "Update fnllm to latest. Update default graphrag configuration", "type": "minor"}, {"description": "A few fixes and enhancements for better reuse and flow.", "type": "patch"}, {"description": "Add full llm response to LLM PRovider output", "type": "patch"}, {"description": "Fix Drift Reduce Response for non streaming calls", "type": "patch"}, {"description": "Fix global search prompt to include missing formatting key", "type": "patch"}, {"description": "Upgrade pyarrow dependency to >=17.0.0 to fix CVE-2024-52338", "type": "patch"}], "created_at": "2025-05-23T21:02:47+00:00", "version": "2.3.0"}