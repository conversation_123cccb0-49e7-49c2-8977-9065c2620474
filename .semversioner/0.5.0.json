{"changes": [{"description": "Data model changes.", "type": "minor"}, {"description": "Add Parquet as part of the default emitters when not pressent", "type": "patch"}, {"description": "Centralized prompts and export all for easier injection.", "type": "patch"}, {"description": "Cleanup of artifact outputs/schemas.", "type": "patch"}, {"description": "Config and docs updates.", "type": "patch"}, {"description": "Implement dynamic community selection to global search", "type": "patch"}, {"description": "fix autocompletion of existing files/directory paths.", "type": "patch"}, {"description": "move import statements out of init files", "type": "patch"}], "created_at": "2024-11-16T00:43:06+00:00", "version": "0.5.0"}