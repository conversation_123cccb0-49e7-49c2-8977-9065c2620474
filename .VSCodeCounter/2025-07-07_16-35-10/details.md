# Details

Date : 2025-07-07 16:35:10

Directory /home/<USER>/debug/graphrag

Total : 576 files,  50357 codes, 1847 comments, 10025 blanks, all 62229 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.github/ISSUE\_TEMPLATE/bug\_report.yml](/.github/ISSUE_TEMPLATE/bug_report.yml) | YAML | 67 | 1 | 4 | 72 |
| [.github/ISSUE\_TEMPLATE/config.yml](/.github/ISSUE_TEMPLATE/config.yml) | YAML | 1 | 0 | 1 | 2 |
| [.github/ISSUE\_TEMPLATE/feature\_request.yml](/.github/ISSUE_TEMPLATE/feature_request.yml) | YAML | 32 | 0 | 4 | 36 |
| [.github/ISSUE\_TEMPLATE/general\_issue.yml](/.github/ISSUE_TEMPLATE/general_issue.yml) | YAML | 61 | 1 | 4 | 66 |
| [.github/dependabot.yml](/.github/dependabot.yml) | YAML | 10 | 5 | 1 | 16 |
| [.github/pull\_request\_template.md](/.github/pull_request_template.md) | Markdown | 13 | 13 | 11 | 37 |
| [.github/workflows/gh-pages.yml](/.github/workflows/gh-pages.yml) | YAML | 44 | 0 | 10 | 54 |
| [.github/workflows/issues-autoresolve.yml](/.github/workflows/issues-autoresolve.yml) | YAML | 27 | 0 | 3 | 30 |
| [.github/workflows/python-ci.yml](/.github/workflows/python-ci.yml) | YAML | 78 | 2 | 13 | 93 |
| [.github/workflows/python-integration-tests.yml](/.github/workflows/python-integration-tests.yml) | YAML | 81 | 6 | 14 | 101 |
| [.github/workflows/python-notebook-tests.yml](/.github/workflows/python-notebook-tests.yml) | YAML | 71 | 2 | 11 | 84 |
| [.github/workflows/python-publish.yml](/.github/workflows/python-publish.yml) | YAML | 46 | 0 | 10 | 56 |
| [.github/workflows/python-smoke-tests.yml](/.github/workflows/python-smoke-tests.yml) | YAML | 96 | 4 | 14 | 114 |
| [.github/workflows/semver.yml](/.github/workflows/semver.yml) | YAML | 19 | 1 | 1 | 21 |
| [.github/workflows/spellcheck.yml](/.github/workflows/spellcheck.yml) | YAML | 20 | 1 | 2 | 23 |
| [.semversioner/0.1.0.json](/.semversioner/0.1.0.json) | JSON | 10 | 0 | 0 | 10 |
| [.semversioner/0.2.0.json](/.semversioner/0.2.0.json) | JSON | 94 | 0 | 1 | 95 |
| [.semversioner/0.2.1.json](/.semversioner/0.2.1.json) | JSON | 70 | 0 | 0 | 70 |
| [.semversioner/0.2.2.json](/.semversioner/0.2.2.json) | JSON | 22 | 0 | 0 | 22 |
| [.semversioner/0.3.0.json](/.semversioner/0.3.0.json) | JSON | 30 | 0 | 0 | 30 |
| [.semversioner/0.3.1.json](/.semversioner/0.3.1.json) | JSON | 34 | 0 | 0 | 34 |
| [.semversioner/0.3.2.json](/.semversioner/0.3.2.json) | JSON | 42 | 0 | 0 | 42 |
| [.semversioner/0.3.3.json](/.semversioner/0.3.3.json) | JSON | 66 | 0 | 0 | 66 |
| [.semversioner/0.3.4.json](/.semversioner/0.3.4.json) | JSON | 14 | 0 | 0 | 14 |
| [.semversioner/0.3.5.json](/.semversioner/0.3.5.json) | JSON | 50 | 0 | 0 | 50 |
| [.semversioner/0.3.6.json](/.semversioner/0.3.6.json) | JSON | 14 | 0 | 0 | 14 |
| [.semversioner/0.4.0.json](/.semversioner/0.4.0.json) | JSON | 222 | 0 | 0 | 222 |
| [.semversioner/0.4.1.json](/.semversioner/0.4.1.json) | JSON | 34 | 0 | 0 | 34 |
| [.semversioner/0.5.0.json](/.semversioner/0.5.0.json) | JSON | 38 | 0 | 0 | 38 |
| [.semversioner/0.9.0.json](/.semversioner/0.9.0.json) | JSON | 46 | 0 | 0 | 46 |
| [.semversioner/1.0.0.json](/.semversioner/1.0.0.json) | JSON | 26 | 0 | 0 | 26 |
| [.semversioner/1.0.1.json](/.semversioner/1.0.1.json) | JSON | 22 | 0 | 0 | 22 |
| [.semversioner/1.1.0.json](/.semversioner/1.1.0.json) | JSON | 58 | 0 | 0 | 58 |
| [.semversioner/1.1.1.json](/.semversioner/1.1.1.json) | JSON | 14 | 0 | 0 | 14 |
| [.semversioner/1.1.2.json](/.semversioner/1.1.2.json) | JSON | 10 | 0 | 0 | 10 |
| [.semversioner/1.2.0.json](/.semversioner/1.2.0.json) | JSON | 26 | 0 | 0 | 26 |
| [.semversioner/2.0.0.json](/.semversioner/2.0.0.json) | JSON | 146 | 0 | 0 | 146 |
| [.semversioner/2.1.0.json](/.semversioner/2.1.0.json) | JSON | 22 | 0 | 0 | 22 |
| [.semversioner/2.2.0.json](/.semversioner/2.2.0.json) | JSON | 46 | 0 | 0 | 46 |
| [.semversioner/2.2.1.json](/.semversioner/2.2.1.json) | JSON | 18 | 0 | 0 | 18 |
| [.semversioner/2.3.0.json](/.semversioner/2.3.0.json) | JSON | 34 | 0 | 0 | 34 |
| [.semversioner/next-release/minor-20250519234123676262.json](/.semversioner/next-release/minor-20250519234123676262.json) | JSON | 4 | 0 | 1 | 5 |
| [.semversioner/next-release/patch-20250530204951787463.json](/.semversioner/next-release/patch-20250530204951787463.json) | JSON | 4 | 0 | 1 | 5 |
| [.venv/lib/python3.12/site-packages/mkdocs\_jupyter/templates/mkdocs\_html/assets/clipboard.umd.js](/.venv/lib/python3.12/site-packages/mkdocs_jupyter/templates/mkdocs_html/assets/clipboard.umd.js) | JavaScript | 119 | 4 | 28 | 151 |
| [.vsts-ci.yml](/.vsts-ci.yml) | YAML | 36 | 0 | 5 | 41 |
| [CHANGELOG.md](/CHANGELOG.md) | Markdown | 291 | 0 | 53 | 344 |
| [CODE\_OF\_CONDUCT.md](/CODE_OF_CONDUCT.md) | Markdown | 6 | 0 | 4 | 10 |
| [CONTRIBUTING.md](/CONTRIBUTING.md) | Markdown | 52 | 0 | 28 | 80 |
| [DEVELOPING.md](/DEVELOPING.md) | Markdown | 93 | 0 | 30 | 123 |
| [RAI\_TRANSPARENCY.md](/RAI_TRANSPARENCY.md) | Markdown | 22 | 0 | 19 | 41 |
| [README.md](/README.md) | Markdown | 53 | 0 | 25 | 78 |
| [SECURITY.md](/SECURITY.md) | Markdown | 22 | 0 | 15 | 37 |
| [SUPPORT.md](/SUPPORT.md) | Markdown | 16 | 0 | 12 | 28 |
| [breaking-changes.md](/breaking-changes.md) | Markdown | 57 | 0 | 36 | 93 |
| [cspell.config.yaml](/cspell.config.yaml) | YAML | 31 | 0 | 1 | 32 |
| [docs/blog\_posts.md](/docs/blog_posts.md) | Markdown | 30 | 0 | 24 | 54 |
| [docs/cli.md](/docs/cli.md) | Markdown | 7 | 0 | 3 | 10 |
| [docs/config/env\_vars.md](/docs/config/env_vars.md) | Markdown | 173 | 0 | 47 | 220 |
| [docs/config/init.md](/docs/config/init.md) | Markdown | 20 | 0 | 13 | 33 |
| [docs/config/models.md](/docs/config/models.md) | Markdown | 70 | 0 | 31 | 101 |
| [docs/config/overview.md](/docs/config/overview.md) | Markdown | 7 | 0 | 5 | 12 |
| [docs/config/yaml.md](/docs/config/yaml.md) | Markdown | 288 | 0 | 102 | 390 |
| [docs/data/operation\_dulce/ABOUT.md](/docs/data/operation_dulce/ABOUT.md) | Markdown | 2 | 0 | 1 | 3 |
| [docs/data/operation\_dulce/Operation Dulce v2 1 1.md](/docs/data/operation_dulce/Operation%20Dulce%20v2%201%201.md) | Markdown | 485 | 0 | 486 | 971 |
| [docs/developing.md](/docs/developing.md) | Markdown | 58 | 0 | 29 | 87 |
| [docs/examples\_notebooks/api\_overview.ipynb](/docs/examples_notebooks/api_overview.ipynb) | JSON | 190 | 0 | 1 | 191 |
| [docs/examples\_notebooks/drift\_search.ipynb](/docs/examples_notebooks/drift_search.ipynb) | JSON | 234 | 0 | 1 | 235 |
| [docs/examples\_notebooks/global\_search.ipynb](/docs/examples_notebooks/global_search.ipynb) | JSON | 263 | 0 | 1 | 264 |
| [docs/examples\_notebooks/global\_search\_with\_dynamic\_community\_selection.ipynb](/docs/examples_notebooks/global_search_with_dynamic_community_selection.ipynb) | JSON | 295 | 0 | 1 | 296 |
| [docs/examples\_notebooks/index\_migration\_to\_v1.ipynb](/docs/examples_notebooks/index_migration_to_v1.ipynb) | JSON | 261 | 0 | 1 | 262 |
| [docs/examples\_notebooks/index\_migration\_to\_v2.ipynb](/docs/examples_notebooks/index_migration_to_v2.ipynb) | JSON | 171 | 0 | 1 | 172 |
| [docs/examples\_notebooks/inputs/operation dulce/ABOUT.md](/docs/examples_notebooks/inputs/operation%20dulce/ABOUT.md) | Markdown | 2 | 0 | 2 | 4 |
| [docs/examples\_notebooks/inputs/operation dulce/Operation Dulce v2 1 1.md](/docs/examples_notebooks/inputs/operation%20dulce/Operation%20Dulce%20v2%201%201.md) | Markdown | 485 | 0 | 486 | 971 |
| [docs/examples\_notebooks/local\_search.ipynb](/docs/examples_notebooks/local_search.ipynb) | JSON | 473 | 0 | 1 | 474 |
| [docs/examples\_notebooks/multi\_index\_search.ipynb](/docs/examples_notebooks/multi_index_search.ipynb) | JSON | 558 | 0 | 1 | 559 |
| [docs/get\_started.md](/docs/get_started.md) | Markdown | 80 | 0 | 40 | 120 |
| [docs/index.md](/docs/index.md) | Markdown | 41 | 0 | 25 | 66 |
| [docs/index/architecture.md](/docs/index/architecture.md) | Markdown | 26 | 0 | 9 | 35 |
| [docs/index/byog.md](/docs/index/byog.md) | Markdown | 41 | 0 | 30 | 71 |
| [docs/index/default\_dataflow.md](/docs/index/default_dataflow.md) | Markdown | 152 | 0 | 60 | 212 |
| [docs/index/inputs.md](/docs/index/inputs.md) | Markdown | 176 | 0 | 94 | 270 |
| [docs/index/methods.md](/docs/index/methods.md) | Markdown | 27 | 0 | 18 | 45 |
| [docs/index/outputs.md](/docs/index/outputs.md) | Markdown | 93 | 0 | 17 | 110 |
| [docs/index/overview.md](/docs/index/overview.md) | Markdown | 26 | 0 | 16 | 42 |
| [docs/prompt\_tuning/auto\_prompt\_tuning.md](/docs/prompt_tuning/auto_prompt_tuning.md) | Markdown | 63 | 0 | 39 | 102 |
| [docs/prompt\_tuning/manual\_prompt\_tuning.md](/docs/prompt_tuning/manual_prompt_tuning.md) | Markdown | 54 | 0 | 36 | 90 |
| [docs/prompt\_tuning/overview.md](/docs/prompt_tuning/overview.md) | Markdown | 8 | 0 | 8 | 16 |
| [docs/query/drift\_search.md](/docs/query/drift_search.md) | Markdown | 22 | 0 | 15 | 37 |
| [docs/query/global\_search.md](/docs/query/global_search.md) | Markdown | 52 | 0 | 21 | 73 |
| [docs/query/local\_search.md](/docs/query/local_search.md) | Markdown | 45 | 0 | 18 | 63 |
| [docs/query/multi\_index\_search.md](/docs/query/multi_index_search.md) | Markdown | 10 | 0 | 10 | 20 |
| [docs/query/notebooks/overview.md](/docs/query/notebooks/overview.md) | Markdown | 8 | 0 | 6 | 14 |
| [docs/query/overview.md](/docs/query/overview.md) | Markdown | 21 | 0 | 17 | 38 |
| [docs/query/question\_generation.md](/docs/query/question_generation.md) | Markdown | 15 | 0 | 9 | 24 |
| [docs/scripts/create\_cookie\_banner.js](/docs/scripts/create_cookie_banner.js) | JavaScript | 15 | 0 | 3 | 18 |
| [docs/stylesheets/extra.css](/docs/stylesheets/extra.css) | CSS | 25 | 0 | 4 | 29 |
| [docs/visualization\_guide.md](/docs/visualization_guide.md) | Markdown | 78 | 0 | 23 | 101 |
| [examples\_notebooks/community\_contrib/README.md](/examples_notebooks/community_contrib/README.md) | Markdown | 3 | 0 | 3 | 6 |
| [examples\_notebooks/community\_contrib/neo4j/graphrag\_import\_neo4j\_cypher.ipynb](/examples_notebooks/community_contrib/neo4j/graphrag_import_neo4j_cypher.ipynb) | JSON | 1,215 | 0 | 1 | 1,216 |
| [examples\_notebooks/community\_contrib/yfiles-jupyter-graphs/graph-visualization.ipynb](/examples_notebooks/community_contrib/yfiles-jupyter-graphs/graph-visualization.ipynb) | JSON | 523 | 0 | 1 | 524 |
| [export\_entities\_to\_postgres.py](/export_entities_to_postgres.py) | Python | 250 | 16 | 52 | 318 |
| [graphrag/\_\_init\_\_.py](/graphrag/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/\_\_main\_\_.py](/graphrag/__main__.py) | Python | 3 | 2 | 4 | 9 |
| [graphrag/api/\_\_init\_\_.py](/graphrag/api/__init__.py) | Python | 38 | 5 | 5 | 48 |
| [graphrag/api/index.py](/graphrag/api/index.py) | Python | 75 | 4 | 21 | 100 |
| [graphrag/api/prompt\_tune.py](/graphrag/api/prompt_tune.py) | Python | 169 | 5 | 25 | 199 |
| [graphrag/api/query.py](/graphrag/api/query.py) | Python | 1,042 | 31 | 128 | 1,201 |
| [graphrag/cache/\_\_init\_\_.py](/graphrag/cache/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/cache/factory.py](/graphrag/cache/factory.py) | Python | 48 | 2 | 14 | 64 |
| [graphrag/cache/json\_pipeline\_cache.py](/graphrag/cache/json_pipeline_cache.py) | Python | 49 | 2 | 15 | 66 |
| [graphrag/cache/memory\_pipeline\_cache.py](/graphrag/cache/memory_pipeline_cache.py) | Python | 56 | 2 | 21 | 79 |
| [graphrag/cache/noop\_pipeline\_cache.py](/graphrag/cache/noop_pipeline_cache.py) | Python | 45 | 2 | 19 | 66 |
| [graphrag/cache/pipeline\_cache.py](/graphrag/cache/pipeline_cache.py) | Python | 47 | 2 | 19 | 68 |
| [graphrag/callbacks/\_\_init\_\_.py](/graphrag/callbacks/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/callbacks/blob\_workflow\_callbacks.py](/graphrag/callbacks/blob_workflow_callbacks.py) | Python | 87 | 4 | 19 | 110 |
| [graphrag/callbacks/console\_workflow\_callbacks.py](/graphrag/callbacks/console_workflow_callbacks.py) | Python | 21 | 2 | 10 | 33 |
| [graphrag/callbacks/file\_workflow\_callbacks.py](/graphrag/callbacks/file_workflow_callbacks.py) | Python | 62 | 2 | 15 | 79 |
| [graphrag/callbacks/llm\_callbacks.py](/graphrag/callbacks/llm_callbacks.py) | Python | 7 | 2 | 6 | 15 |
| [graphrag/callbacks/noop\_query\_callbacks.py](/graphrag/callbacks/noop_query_callbacks.py) | Python | 20 | 2 | 12 | 34 |
| [graphrag/callbacks/noop\_workflow\_callbacks.py](/graphrag/callbacks/noop_workflow_callbacks.py) | Python | 28 | 2 | 13 | 43 |
| [graphrag/callbacks/progress\_workflow\_callbacks.py](/graphrag/callbacks/progress_workflow_callbacks.py) | Python | 28 | 2 | 13 | 43 |
| [graphrag/callbacks/query\_callbacks.py](/graphrag/callbacks/query_callbacks.py) | Python | 20 | 2 | 12 | 34 |
| [graphrag/callbacks/reporting.py](/graphrag/callbacks/reporting.py) | Python | 30 | 2 | 8 | 40 |
| [graphrag/callbacks/workflow\_callbacks.py](/graphrag/callbacks/workflow_callbacks.py) | Python | 39 | 2 | 15 | 56 |
| [graphrag/callbacks/workflow\_callbacks\_manager.py](/graphrag/callbacks/workflow_callbacks_manager.py) | Python | 59 | 2 | 16 | 77 |
| [graphrag/cli/\_\_init\_\_.py](/graphrag/cli/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/cli/index.py](/graphrag/cli/index.py) | Python | 154 | 5 | 33 | 192 |
| [graphrag/cli/initialize.py](/graphrag/cli/initialize.py) | Python | 79 | 2 | 14 | 95 |
| [graphrag/cli/main.py](/graphrag/cli/main.py) | Python | 507 | 11 | 37 | 555 |
| [graphrag/cli/prompt\_tune.py](/graphrag/cli/prompt_tune.py) | Python | 105 | 4 | 13 | 122 |
| [graphrag/cli/query.py](/graphrag/cli/query.py) | Python | 441 | 35 | 68 | 544 |
| [graphrag/config/\_\_init\_\_.py](/graphrag/config/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/config/create\_graphrag\_config.py](/graphrag/config/create_graphrag_config.py) | Python | 33 | 2 | 9 | 44 |
| [graphrag/config/defaults.py](/graphrag/config/defaults.py) | Python | 351 | 2 | 89 | 442 |
| [graphrag/config/embeddings.py](/graphrag/config/embeddings.py) | Python | 38 | 2 | 9 | 49 |
| [graphrag/config/enums.py](/graphrag/config/enums.py) | Python | 113 | 5 | 51 | 169 |
| [graphrag/config/environment\_reader.py](/graphrag/config/environment_reader.py) | Python | 125 | 2 | 29 | 156 |
| [graphrag/config/errors.py](/graphrag/config/errors.py) | Python | 39 | 2 | 19 | 60 |
| [graphrag/config/get\_embedding\_settings.py](/graphrag/config/get_embedding_settings.py) | Python | 25 | 9 | 6 | 40 |
| [graphrag/config/init\_content.py](/graphrag/config/init_content.py) | Python | 115 | 26 | 32 | 173 |
| [graphrag/config/load\_config.py](/graphrag/config/load_config.py) | Python | 154 | 2 | 36 | 192 |
| [graphrag/config/logging.py](/graphrag/config/logging.py) | Python | 48 | 2 | 12 | 62 |
| [graphrag/config/models/\_\_init\_\_.py](/graphrag/config/models/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/config/models/basic\_search\_config.py](/graphrag/config/models/basic_search_config.py) | Python | 25 | 2 | 7 | 34 |
| [graphrag/config/models/cache\_config.py](/graphrag/config/models/cache_config.py) | Python | 30 | 2 | 7 | 39 |
| [graphrag/config/models/chunking\_config.py](/graphrag/config/models/chunking_config.py) | Python | 34 | 2 | 7 | 43 |
| [graphrag/config/models/cluster\_graph\_config.py](/graphrag/config/models/cluster_graph_config.py) | Python | 17 | 2 | 7 | 26 |
| [graphrag/config/models/community\_reports\_config.py](/graphrag/config/models/community_reports_config.py) | Python | 54 | 2 | 10 | 66 |
| [graphrag/config/models/drift\_search\_config.py](/graphrag/config/models/drift_search_config.py) | Python | 97 | 2 | 25 | 124 |
| [graphrag/config/models/embed\_graph\_config.py](/graphrag/config/models/embed_graph_config.py) | Python | 37 | 2 | 7 | 46 |
| [graphrag/config/models/extract\_claims\_config.py](/graphrag/config/models/extract_claims_config.py) | Python | 45 | 2 | 9 | 56 |
| [graphrag/config/models/extract\_graph\_config.py](/graphrag/config/models/extract_graph_config.py) | Python | 44 | 2 | 10 | 56 |
| [graphrag/config/models/extract\_graph\_nlp\_config.py](/graphrag/config/models/extract_graph_nlp_config.py) | Python | 59 | 2 | 10 | 71 |
| [graphrag/config/models/global\_search\_config.py](/graphrag/config/models/global_search_config.py) | Python | 57 | 3 | 8 | 68 |
| [graphrag/config/models/graph\_rag\_config.py](/graphrag/config/models/graph_rag_config.py) | Python | 304 | 2 | 58 | 364 |
| [graphrag/config/models/input\_config.py](/graphrag/config/models/input_config.py) | Python | 42 | 2 | 7 | 51 |
| [graphrag/config/models/language\_model\_config.py](/graphrag/config/models/language_model_config.py) | Python | 277 | 5 | 47 | 329 |
| [graphrag/config/models/local\_search\_config.py](/graphrag/config/models/local_search_config.py) | Python | 41 | 2 | 7 | 50 |
| [graphrag/config/models/prune\_graph\_config.py](/graphrag/config/models/prune_graph_config.py) | Python | 33 | 2 | 7 | 42 |
| [graphrag/config/models/reporting\_config.py](/graphrag/config/models/reporting_config.py) | Python | 26 | 2 | 7 | 35 |
| [graphrag/config/models/snapshots\_config.py](/graphrag/config/models/snapshots_config.py) | Python | 17 | 2 | 7 | 26 |
| [graphrag/config/models/storage\_config.py](/graphrag/config/models/storage_config.py) | Python | 38 | 5 | 10 | 53 |
| [graphrag/config/models/summarize\_descriptions\_config.py](/graphrag/config/models/summarize_descriptions_config.py) | Python | 45 | 2 | 10 | 57 |
| [graphrag/config/models/text\_embedding\_config.py](/graphrag/config/models/text_embedding_config.py) | Python | 42 | 2 | 9 | 53 |
| [graphrag/config/models/umap\_config.py](/graphrag/config/models/umap_config.py) | Python | 9 | 2 | 7 | 18 |
| [graphrag/config/models/vector\_store\_config.py](/graphrag/config/models/vector_store_config.py) | Python | 72 | 2 | 20 | 94 |
| [graphrag/config/read\_dotenv.py](/graphrag/config/read_dotenv.py) | Python | 17 | 2 | 7 | 26 |
| [graphrag/data\_model/\_\_init\_\_.py](/graphrag/data_model/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/data\_model/community.py](/graphrag/data_model/community.py) | Python | 61 | 2 | 17 | 80 |
| [graphrag/data\_model/community\_report.py](/graphrag/data_model/community_report.py) | Python | 51 | 2 | 15 | 68 |
| [graphrag/data\_model/covariate.py](/graphrag/data_model/covariate.py) | Python | 40 | 2 | 13 | 55 |
| [graphrag/data\_model/document.py](/graphrag/data_model/document.py) | Python | 37 | 2 | 11 | 50 |
| [graphrag/data\_model/entity.py](/graphrag/data_model/entity.py) | Python | 53 | 2 | 15 | 70 |
| [graphrag/data\_model/identified.py](/graphrag/data_model/identified.py) | Python | 9 | 2 | 7 | 18 |
| [graphrag/data\_model/named.py](/graphrag/data_model/named.py) | Python | 8 | 2 | 7 | 17 |
| [graphrag/data\_model/relationship.py](/graphrag/data_model/relationship.py) | Python | 49 | 2 | 15 | 66 |
| [graphrag/data\_model/schemas.py](/graphrag/data_model/schemas.py) | Python | 138 | 11 | 21 | 170 |
| [graphrag/data\_model/text\_unit.py](/graphrag/data_model/text_unit.py) | Python | 47 | 2 | 14 | 63 |
| [graphrag/data\_model/types.py](/graphrag/data_model/types.py) | Python | 3 | 2 | 4 | 9 |
| [graphrag/index/\_\_init\_\_.py](/graphrag/index/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/input/\_\_init\_\_.py](/graphrag/index/input/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/input/csv.py](/graphrag/index/input/csv.py) | Python | 31 | 2 | 13 | 46 |
| [graphrag/index/input/factory.py](/graphrag/index/input/factory.py) | Python | 46 | 4 | 12 | 62 |
| [graphrag/index/input/json.py](/graphrag/index/input/json.py) | Python | 33 | 3 | 14 | 50 |
| [graphrag/index/input/text.py](/graphrag/index/input/text.py) | Python | 26 | 2 | 10 | 38 |
| [graphrag/index/input/util.py](/graphrag/index/input/util.py) | Python | 74 | 2 | 14 | 90 |
| [graphrag/index/operations/\_\_init\_\_.py](/graphrag/index/operations/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/build\_noun\_graph/\_\_init\_\_.py](/graphrag/index/operations/build_noun_graph/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/build\_noun\_graph/build\_noun\_graph.py](/graphrag/index/operations/build_noun_graph/build_noun_graph.py) | Python | 107 | 5 | 22 | 134 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/\_\_init\_\_.py](/graphrag/index/operations/build_noun_graph/np_extractors/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/base.py](/graphrag/index/operations/build_noun_graph/np_extractors/base.py) | Python | 46 | 2 | 14 | 62 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/cfg\_extractor.py](/graphrag/index/operations/build_noun_graph/np_extractors/cfg_extractor.py) | Python | 156 | 5 | 21 | 182 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/factory.py](/graphrag/index/operations/build_noun_graph/np_extractors/factory.py) | Python | 70 | 2 | 11 | 83 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/np\_validator.py](/graphrag/index/operations/build_noun_graph/np_extractors/np_validator.py) | Python | 16 | 2 | 8 | 26 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/regex\_extractor.py](/graphrag/index/operations/build_noun_graph/np_extractors/regex_extractor.py) | Python | 100 | 6 | 18 | 124 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/resource\_loader.py](/graphrag/index/operations/build_noun_graph/np_extractors/resource_loader.py) | Python | 28 | 5 | 6 | 39 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/stop\_words.py](/graphrag/index/operations/build_noun_graph/np_extractors/stop_words.py) | Python | 17 | 2 | 3 | 22 |
| [graphrag/index/operations/build\_noun\_graph/np\_extractors/syntactic\_parsing\_extractor.py](/graphrag/index/operations/build_noun_graph/np_extractors/syntactic_parsing_extractor.py) | Python | 136 | 5 | 22 | 163 |
| [graphrag/index/operations/chunk\_text/\_\_init\_\_.py](/graphrag/index/operations/chunk_text/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/chunk\_text/bootstrap.py](/graphrag/index/operations/chunk_text/bootstrap.py) | Python | 21 | 3 | 8 | 32 |
| [graphrag/index/operations/chunk\_text/chunk\_text.py](/graphrag/index/operations/chunk_text/chunk_text.py) | Python | 103 | 11 | 27 | 141 |
| [graphrag/index/operations/chunk\_text/strategies.py](/graphrag/index/operations/chunk_text/strategies.py) | Python | 53 | 2 | 15 | 70 |
| [graphrag/index/operations/chunk\_text/typing.py](/graphrag/index/operations/chunk_text/typing.py) | Python | 16 | 2 | 10 | 28 |
| [graphrag/index/operations/cluster\_graph.py](/graphrag/index/operations/cluster_graph.py) | Python | 59 | 3 | 19 | 81 |
| [graphrag/index/operations/compute\_degree.py](/graphrag/index/operations/compute_degree.py) | Python | 9 | 2 | 5 | 16 |
| [graphrag/index/operations/compute\_edge\_combined\_degree.py](/graphrag/index/operations/compute_edge_combined_degree.py) | Python | 32 | 2 | 10 | 44 |
| [graphrag/index/operations/create\_graph.py](/graphrag/index/operations/create_graph.py) | Python | 15 | 2 | 7 | 24 |
| [graphrag/index/operations/embed\_graph/\_\_init\_\_.py](/graphrag/index/operations/embed_graph/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/embed\_graph/embed\_graph.py](/graphrag/index/operations/embed_graph/embed_graph.py) | Python | 37 | 4 | 10 | 51 |
| [graphrag/index/operations/embed\_graph/embed\_node2vec.py](/graphrag/index/operations/embed_graph/embed_node2vec.py) | Python | 30 | 4 | 10 | 44 |
| [graphrag/index/operations/embed\_graph/typing.py](/graphrag/index/operations/embed_graph/typing.py) | Python | 6 | 3 | 4 | 13 |
| [graphrag/index/operations/embed\_text/\_\_init\_\_.py](/graphrag/index/operations/embed_text/__init__.py) | Python | 6 | 2 | 4 | 12 |
| [graphrag/index/operations/embed\_text/embed\_text.py](/graphrag/index/operations/embed_text/embed_text.py) | Python | 173 | 5 | 42 | 220 |
| [graphrag/index/operations/embed\_text/strategies/\_\_init\_\_.py](/graphrag/index/operations/embed_text/strategies/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/embed\_text/strategies/mock.py](/graphrag/index/operations/embed_text/strategies/mock.py) | Python | 24 | 2 | 8 | 34 |
| [graphrag/index/operations/embed\_text/strategies/openai.py](/graphrag/index/operations/embed_text/strategies/openai.py) | Python | 135 | 8 | 30 | 173 |
| [graphrag/index/operations/embed\_text/strategies/typing.py](/graphrag/index/operations/embed_text/strategies/typing.py) | Python | 18 | 2 | 9 | 29 |
| [graphrag/index/operations/extract\_covariates/\_\_init\_\_.py](/graphrag/index/operations/extract_covariates/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/extract\_covariates/claim\_extractor.py](/graphrag/index/operations/extract_covariates/claim_extractor.py) | Python | 199 | 8 | 30 | 237 |
| [graphrag/index/operations/extract\_covariates/extract\_covariates.py](/graphrag/index/operations/extract_covariates/extract_covariates.py) | Python | 125 | 2 | 26 | 153 |
| [graphrag/index/operations/extract\_covariates/typing.py](/graphrag/index/operations/extract_covariates/typing.py) | Python | 36 | 2 | 12 | 50 |
| [graphrag/index/operations/extract\_graph/\_\_init\_\_.py](/graphrag/index/operations/extract_graph/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/extract\_graph/extract\_graph.py](/graphrag/index/operations/extract_graph/extract_graph.py) | Python | 107 | 8 | 31 | 146 |
| [graphrag/index/operations/extract\_graph/graph\_extractor.py](/graphrag/index/operations/extract_graph/graph_extractor.py) | Python | 263 | 12 | 37 | 312 |
| [graphrag/index/operations/extract\_graph/graph\_intelligence\_strategy.py](/graphrag/index/operations/extract_graph/graph_intelligence_strategy.py) | Python | 83 | 3 | 17 | 103 |
| [graphrag/index/operations/extract\_graph/typing.py](/graphrag/index/operations/extract_graph/typing.py) | Python | 40 | 2 | 18 | 60 |
| [graphrag/index/operations/finalize\_community\_reports.py](/graphrag/index/operations/finalize_community_reports.py) | Python | 22 | 3 | 9 | 34 |
| [graphrag/index/operations/finalize\_entities.py](/graphrag/index/operations/finalize_entities.py) | Python | 52 | 4 | 9 | 65 |
| [graphrag/index/operations/finalize\_relationships.py](/graphrag/index/operations/finalize_relationships.py) | Python | 33 | 2 | 10 | 45 |
| [graphrag/index/operations/graph\_to\_dataframes.py](/graphrag/index/operations/graph_to_dataframes.py) | Python | 24 | 5 | 10 | 39 |
| [graphrag/index/operations/layout\_graph/\_\_init\_\_.py](/graphrag/index/operations/layout_graph/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/layout\_graph/layout\_graph.py](/graphrag/index/operations/layout_graph/layout_graph.py) | Python | 62 | 5 | 14 | 81 |
| [graphrag/index/operations/layout\_graph/typing.py](/graphrag/index/operations/layout_graph/typing.py) | Python | 15 | 3 | 10 | 28 |
| [graphrag/index/operations/layout\_graph/umap.py](/graphrag/index/operations/layout_graph/umap.py) | Python | 105 | 8 | 20 | 133 |
| [graphrag/index/operations/layout\_graph/zero.py](/graphrag/index/operations/layout_graph/zero.py) | Python | 74 | 7 | 16 | 97 |
| [graphrag/index/operations/prune\_graph.py](/graphrag/index/operations/prune_graph.py) | Python | 71 | 7 | 15 | 93 |
| [graphrag/index/operations/snapshot\_graphml.py](/graphrag/index/operations/snapshot_graphml.py) | Python | 11 | 2 | 6 | 19 |
| [graphrag/index/operations/summarize\_communities/\_\_init\_\_.py](/graphrag/index/operations/summarize_communities/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/summarize\_communities/build\_mixed\_context.py](/graphrag/index/operations/summarize_communities/build_mixed_context.py) | Python | 54 | 6 | 11 | 71 |
| [graphrag/index/operations/summarize\_communities/community\_reports\_extractor.py](/graphrag/index/operations/summarize_communities/community_reports_extractor.py) | Python | 76 | 3 | 24 | 103 |
| [graphrag/index/operations/summarize\_communities/explode\_communities.py](/graphrag/index/operations/summarize_communities/explode_communities.py) | Python | 16 | 2 | 6 | 24 |
| [graphrag/index/operations/summarize\_communities/graph\_context/\_\_init\_\_.py](/graphrag/index/operations/summarize_communities/graph_context/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/summarize\_communities/graph\_context/context\_builder.py](/graphrag/index/operations/summarize_communities/graph_context/context_builder.py) | Python | 267 | 26 | 52 | 345 |
| [graphrag/index/operations/summarize\_communities/graph\_context/sort\_context.py](/graphrag/index/operations/summarize_communities/graph_context/sort_context.py) | Python | 119 | 13 | 25 | 157 |
| [graphrag/index/operations/summarize\_communities/strategies.py](/graphrag/index/operations/summarize_communities/strategies.py) | Python | 78 | 3 | 12 | 93 |
| [graphrag/index/operations/summarize\_communities/summarize\_communities.py](/graphrag/index/operations/summarize_communities/summarize_communities.py) | Python | 106 | 2 | 20 | 128 |
| [graphrag/index/operations/summarize\_communities/text\_unit\_context/\_\_init\_\_.py](/graphrag/index/operations/summarize_communities/text_unit_context/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/operations/summarize\_communities/text\_unit\_context/context\_builder.py](/graphrag/index/operations/summarize_communities/text_unit_context/context_builder.py) | Python | 193 | 12 | 27 | 232 |
| [graphrag/index/operations/summarize\_communities/text\_unit\_context/prep\_text\_units.py](/graphrag/index/operations/summarize_communities/text_unit_context/prep_text_units.py) | Python | 35 | 2 | 9 | 46 |
| [graphrag/index/operations/summarize\_communities/text\_unit\_context/sort\_context.py](/graphrag/index/operations/summarize_communities/text_unit_context/sort_context.py) | Python | 67 | 2 | 16 | 85 |
| [graphrag/index/operations/summarize\_communities/typing.py](/graphrag/index/operations/summarize_communities/typing.py) | Python | 44 | 2 | 18 | 64 |
| [graphrag/index/operations/summarize\_communities/utils.py](/graphrag/index/operations/summarize_communities/utils.py) | Python | 10 | 2 | 6 | 18 |
| [graphrag/index/operations/summarize\_descriptions/\_\_init\_\_.py](/graphrag/index/operations/summarize_descriptions/__init__.py) | Python | 13 | 2 | 4 | 19 |
| [graphrag/index/operations/summarize\_descriptions/description\_summary\_extractor.py](/graphrag/index/operations/summarize_descriptions/description_summary_extractor.py) | Python | 99 | 11 | 24 | 134 |
| [graphrag/index/operations/summarize\_descriptions/graph\_intelligence\_strategy.py](/graphrag/index/operations/summarize_descriptions/graph_intelligence_strategy.py) | Python | 54 | 3 | 9 | 66 |
| [graphrag/index/operations/summarize\_descriptions/summarize\_descriptions.py](/graphrag/index/operations/summarize_descriptions/summarize_descriptions.py) | Python | 96 | 2 | 23 | 121 |
| [graphrag/index/operations/summarize\_descriptions/typing.py](/graphrag/index/operations/summarize_descriptions/typing.py) | Python | 32 | 2 | 17 | 51 |
| [graphrag/index/run/\_\_init\_\_.py](/graphrag/index/run/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/run/run\_pipeline.py](/graphrag/index/run/run_pipeline.py) | Python | 118 | 6 | 27 | 151 |
| [graphrag/index/run/utils.py](/graphrag/index/run/utils.py) | Python | 57 | 2 | 10 | 69 |
| [graphrag/index/text\_splitting/\_\_init\_\_.py](/graphrag/index/text_splitting/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/text\_splitting/check\_token\_limit.py](/graphrag/index/text_splitting/check_token_limit.py) | Python | 9 | 2 | 5 | 16 |
| [graphrag/index/text\_splitting/text\_splitting.py](/graphrag/index/text_splitting/text_splitting.py) | Python | 153 | 6 | 39 | 198 |
| [graphrag/index/typing/\_\_init\_\_.py](/graphrag/index/typing/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/typing/context.py](/graphrag/index/typing/context.py) | Python | 26 | 3 | 7 | 36 |
| [graphrag/index/typing/error\_handler.py](/graphrag/index/typing/error_handler.py) | Python | 3 | 2 | 4 | 9 |
| [graphrag/index/typing/pipeline.py](/graphrag/index/typing/pipeline.py) | Python | 13 | 2 | 9 | 24 |
| [graphrag/index/typing/pipeline\_run\_result.py](/graphrag/index/typing/pipeline_run_result.py) | Python | 14 | 2 | 7 | 23 |
| [graphrag/index/typing/state.py](/graphrag/index/typing/state.py) | Python | 3 | 2 | 4 | 9 |
| [graphrag/index/typing/stats.py](/graphrag/index/typing/stats.py) | Python | 15 | 2 | 9 | 26 |
| [graphrag/index/typing/workflow.py](/graphrag/index/typing/workflow.py) | Python | 18 | 2 | 9 | 29 |
| [graphrag/index/update/\_\_init\_\_.py](/graphrag/index/update/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/update/communities.py](/graphrag/index/update/communities.py) | Python | 109 | 16 | 27 | 152 |
| [graphrag/index/update/entities.py](/graphrag/index/update/entities.py) | Python | 54 | 10 | 15 | 79 |
| [graphrag/index/update/incremental\_index.py](/graphrag/index/update/incremental_index.py) | Python | 56 | 6 | 22 | 84 |
| [graphrag/index/update/relationships.py](/graphrag/index/update/relationships.py) | Python | 59 | 11 | 16 | 86 |
| [graphrag/index/utils/\_\_init\_\_.py](/graphrag/index/utils/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/index/utils/dataframes.py](/graphrag/index/utils/dataframes.py) | Python | 33 | 2 | 19 | 54 |
| [graphrag/index/utils/derive\_from\_rows.py](/graphrag/index/utils/derive_from_rows.py) | Python | 119 | 3 | 40 | 162 |
| [graphrag/index/utils/dicts.py](/graphrag/index/utils/dicts.py) | Python | 16 | 2 | 5 | 23 |
| [graphrag/index/utils/graphs.py](/graphrag/index/utils/graphs.py) | Python | 208 | 2 | 33 | 243 |
| [graphrag/index/utils/hashing.py](/graphrag/index/utils/hashing.py) | Python | 8 | 2 | 5 | 15 |
| [graphrag/index/utils/is\_null.py](/graphrag/index/utils/is_null.py) | Python | 10 | 2 | 8 | 20 |
| [graphrag/index/utils/rate\_limiter.py](/graphrag/index/utils/rate_limiter.py) | Python | 27 | 3 | 11 | 41 |
| [graphrag/index/utils/stable\_lcc.py](/graphrag/index/utils/stable_lcc.py) | Python | 36 | 13 | 19 | 68 |
| [graphrag/index/utils/string.py](/graphrag/index/utils/string.py) | Python | 10 | 4 | 6 | 20 |
| [graphrag/index/utils/tokens.py](/graphrag/index/utils/tokens.py) | Python | 32 | 2 | 11 | 45 |
| [graphrag/index/utils/uuid.py](/graphrag/index/utils/uuid.py) | Python | 8 | 2 | 5 | 15 |
| [graphrag/index/validate\_config.py](/graphrag/index/validate_config.py) | Python | 39 | 5 | 11 | 55 |
| [graphrag/index/workflows/\_\_init\_\_.py](/graphrag/index/workflows/__init__.py) | Python | 92 | 3 | 6 | 101 |
| [graphrag/index/workflows/create\_base\_text\_units.py](/graphrag/index/workflows/create_base_text_units.py) | Python | 121 | 3 | 26 | 150 |
| [graphrag/index/workflows/create\_communities.py](/graphrag/index/workflows/create_communities.py) | Python | 122 | 10 | 20 | 152 |
| [graphrag/index/workflows/create\_community\_reports.py](/graphrag/index/workflows/create_community_reports.py) | Python | 145 | 8 | 31 | 184 |
| [graphrag/index/workflows/create\_community\_reports\_text.py](/graphrag/index/workflows/create_community_reports_text.py) | Python | 85 | 2 | 20 | 107 |
| [graphrag/index/workflows/create\_final\_documents.py](/graphrag/index/workflows/create_final_documents.py) | Python | 53 | 2 | 17 | 72 |
| [graphrag/index/workflows/create\_final\_text\_units.py](/graphrag/index/workflows/create_final_text_units.py) | Python | 93 | 2 | 27 | 122 |
| [graphrag/index/workflows/extract\_covariates.py](/graphrag/index/workflows/extract_covariates.py) | Python | 70 | 4 | 15 | 89 |
| [graphrag/index/workflows/extract\_graph.py](/graphrag/index/workflows/extract_graph.py) | Python | 130 | 4 | 26 | 160 |
| [graphrag/index/workflows/extract\_graph\_nlp.py](/graphrag/index/workflows/extract_graph_nlp.py) | Python | 50 | 3 | 13 | 66 |
| [graphrag/index/workflows/factory.py](/graphrag/index/workflows/factory.py) | Python | 78 | 3 | 13 | 94 |
| [graphrag/index/workflows/finalize\_graph.py](/graphrag/index/workflows/finalize_graph.py) | Python | 58 | 3 | 13 | 74 |
| [graphrag/index/workflows/generate\_text\_embeddings.py](/graphrag/index/workflows/generate_text_embeddings.py) | Python | 164 | 2 | 18 | 184 |
| [graphrag/index/workflows/load\_input\_documents.py](/graphrag/index/workflows/load_input_documents.py) | Python | 31 | 2 | 13 | 46 |
| [graphrag/index/workflows/load\_update\_documents.py](/graphrag/index/workflows/load_update_documents.py) | Python | 42 | 4 | 14 | 60 |
| [graphrag/index/workflows/prune\_graph.py](/graphrag/index/workflows/prune_graph.py) | Python | 59 | 4 | 14 | 77 |
| [graphrag/index/workflows/update\_clean\_state.py](/graphrag/index/workflows/update_clean_state.py) | Python | 20 | 2 | 9 | 31 |
| [graphrag/index/workflows/update\_communities.py](/graphrag/index/workflows/update_communities.py) | Python | 37 | 2 | 14 | 53 |
| [graphrag/index/workflows/update\_community\_reports.py](/graphrag/index/workflows/update_community_reports.py) | Python | 48 | 2 | 16 | 66 |
| [graphrag/index/workflows/update\_covariates.py](/graphrag/index/workflows/update_covariates.py) | Python | 59 | 4 | 18 | 81 |
| [graphrag/index/workflows/update\_entities\_relationships.py](/graphrag/index/workflows/update_entities_relationships.py) | Python | 82 | 4 | 20 | 106 |
| [graphrag/index/workflows/update\_final\_documents.py](/graphrag/index/workflows/update_final_documents.py) | Python | 22 | 2 | 10 | 34 |
| [graphrag/index/workflows/update\_text\_embeddings.py](/graphrag/index/workflows/update_text_embeddings.py) | Python | 47 | 2 | 10 | 59 |
| [graphrag/index/workflows/update\_text\_units.py](/graphrag/index/workflows/update_text_units.py) | Python | 68 | 4 | 20 | 92 |
| [graphrag/language\_model/\_\_init\_\_.py](/graphrag/language_model/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/language\_model/cache/\_\_init\_\_.py](/graphrag/language_model/cache/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/language\_model/cache/base.py](/graphrag/language_model/cache/base.py) | Python | 24 | 2 | 11 | 37 |
| [graphrag/language\_model/events/\_\_init\_\_.py](/graphrag/language_model/events/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/language\_model/events/base.py](/graphrag/language_model/events/base.py) | Python | 12 | 2 | 6 | 20 |
| [graphrag/language\_model/factory.py](/graphrag/language_model/factory.py) | Python | 89 | 3 | 23 | 115 |
| [graphrag/language\_model/manager.py](/graphrag/language_model/manager.py) | Python | 118 | 3 | 31 | 152 |
| [graphrag/language\_model/protocol/\_\_init\_\_.py](/graphrag/language_model/protocol/__init__.py) | Python | 3 | 2 | 4 | 9 |
| [graphrag/language\_model/protocol/base.py](/graphrag/language_model/protocol/base.py) | Python | 125 | 2 | 40 | 167 |
| [graphrag/language\_model/providers/\_\_init\_\_.py](/graphrag/language_model/providers/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/language\_model/providers/fnllm/\_\_init\_\_.py](/graphrag/language_model/providers/fnllm/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/language\_model/providers/fnllm/cache.py](/graphrag/language_model/providers/fnllm/cache.py) | Python | 29 | 2 | 14 | 45 |
| [graphrag/language\_model/providers/fnllm/events.py](/graphrag/language_model/providers/fnllm/events.py) | Python | 16 | 2 | 9 | 27 |
| [graphrag/language\_model/providers/fnllm/models.py](/graphrag/language_model/providers/fnllm/models.py) | Python | 369 | 2 | 73 | 444 |
| [graphrag/language\_model/providers/fnllm/utils.py](/graphrag/language_model/providers/fnllm/utils.py) | Python | 122 | 3 | 34 | 159 |
| [graphrag/language\_model/response/\_\_init\_\_.py](/graphrag/language_model/response/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/language\_model/response/base.py](/graphrag/language_model/response/base.py) | Python | 50 | 2 | 20 | 72 |
| [graphrag/language\_model/response/base.pyi](/graphrag/language_model/response/base.pyi) | Python | 39 | 2 | 10 | 51 |
| [graphrag/logger/\_\_init\_\_.py](/graphrag/logger/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/logger/base.py](/graphrag/logger/base.py) | Python | 47 | 2 | 21 | 70 |
| [graphrag/logger/console.py](/graphrag/logger/console.py) | Python | 16 | 2 | 11 | 29 |
| [graphrag/logger/factory.py](/graphrag/logger/factory.py) | Python | 32 | 3 | 9 | 44 |
| [graphrag/logger/null\_progress.py](/graphrag/logger/null_progress.py) | Python | 23 | 2 | 14 | 39 |
| [graphrag/logger/print\_progress.py](/graphrag/logger/print_progress.py) | Python | 33 | 2 | 16 | 51 |
| [graphrag/logger/progress.py](/graphrag/logger/progress.py) | Python | 57 | 2 | 24 | 83 |
| [graphrag/logger/rich\_progress.py](/graphrag/logger/rich_progress.py) | Python | 129 | 4 | 33 | 166 |
| [graphrag/logger/types.py](/graphrag/logger/types.py) | Python | 12 | 3 | 8 | 23 |
| [graphrag/prompt\_tune/\_\_init\_\_.py](/graphrag/prompt_tune/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/prompt\_tune/defaults.py](/graphrag/prompt_tune/defaults.py) | Python | 14 | 2 | 5 | 21 |
| [graphrag/prompt\_tune/generator/\_\_init\_\_.py](/graphrag/prompt_tune/generator/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/prompt\_tune/generator/community\_report\_rating.py](/graphrag/prompt_tune/generator/community_report_rating.py) | Python | 25 | 2 | 9 | 36 |
| [graphrag/prompt\_tune/generator/community\_report\_summarization.py](/graphrag/prompt_tune/generator/community_report_summarization.py) | Python | 36 | 3 | 12 | 51 |
| [graphrag/prompt\_tune/generator/community\_reporter\_role.py](/graphrag/prompt_tune/generator/community_reporter_role.py) | Python | 25 | 2 | 9 | 36 |
| [graphrag/prompt\_tune/generator/domain.py](/graphrag/prompt_tune/generator/domain.py) | Python | 17 | 2 | 9 | 28 |
| [graphrag/prompt\_tune/generator/entity\_relationship.py](/graphrag/prompt_tune/generator/entity_relationship.py) | Python | 50 | 2 | 14 | 66 |
| [graphrag/prompt\_tune/generator/entity\_summarization\_prompt.py](/graphrag/prompt_tune/generator/entity_summarization_prompt.py) | Python | 26 | 3 | 11 | 40 |
| [graphrag/prompt\_tune/generator/entity\_types.py](/graphrag/prompt_tune/generator/entity_types.py) | Python | 43 | 2 | 15 | 60 |
| [graphrag/prompt\_tune/generator/extract\_graph\_prompt.py](/graphrag/prompt_tune/generator/extract_graph_prompt.py) | Python | 86 | 5 | 19 | 110 |
| [graphrag/prompt\_tune/generator/language.py](/graphrag/prompt_tune/generator/language.py) | Python | 17 | 2 | 9 | 28 |
| [graphrag/prompt\_tune/generator/persona.py](/graphrag/prompt_tune/generator/persona.py) | Python | 18 | 2 | 8 | 28 |
| [graphrag/prompt\_tune/loader/\_\_init\_\_.py](/graphrag/prompt_tune/loader/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/prompt\_tune/loader/input.py](/graphrag/prompt_tune/loader/input.py) | Python | 89 | 5 | 14 | 108 |
| [graphrag/prompt\_tune/prompt/\_\_init\_\_.py](/graphrag/prompt_tune/prompt/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/prompt\_tune/prompt/community\_report\_rating.py](/graphrag/prompt_tune/prompt/community_report_rating.py) | Python | 50 | 28 | 55 | 133 |
| [graphrag/prompt\_tune/prompt/community\_reporter\_role.py](/graphrag/prompt_tune/prompt/community_reporter_role.py) | Python | 13 | 2 | 6 | 21 |
| [graphrag/prompt\_tune/prompt/domain.py](/graphrag/prompt_tune/prompt/domain.py) | Python | 7 | 2 | 4 | 13 |
| [graphrag/prompt\_tune/prompt/entity\_relationship.py](/graphrag/prompt_tune/prompt/entity_relationship.py) | Python | 274 | 30 | 52 | 356 |
| [graphrag/prompt\_tune/prompt/entity\_types.py](/graphrag/prompt_tune/prompt/entity_types.py) | Python | 76 | 2 | 12 | 90 |
| [graphrag/prompt\_tune/prompt/language.py](/graphrag/prompt_tune/prompt/language.py) | Python | 7 | 2 | 4 | 13 |
| [graphrag/prompt\_tune/prompt/persona.py](/graphrag/prompt_tune/prompt/persona.py) | Python | 8 | 2 | 4 | 14 |
| [graphrag/prompt\_tune/template/\_\_init\_\_.py](/graphrag/prompt_tune/template/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/prompt\_tune/template/community\_report\_summarization.py](/graphrag/prompt_tune/template/community_report_summarization.py) | Python | 78 | 7 | 21 | 106 |
| [graphrag/prompt\_tune/template/entity\_summarization.py](/graphrag/prompt_tune/template/entity_summarization.py) | Python | 14 | 4 | 5 | 23 |
| [graphrag/prompt\_tune/template/extract\_graph.py](/graphrag/prompt_tune/template/extract_graph.py) | Python | 95 | 13 | 34 | 142 |
| [graphrag/prompt\_tune/types.py](/graphrag/prompt_tune/types.py) | Python | 11 | 2 | 7 | 20 |
| [graphrag/prompts/\_\_init\_\_.py](/graphrag/prompts/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/prompts/index/\_\_init\_\_.py](/graphrag/prompts/index/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/prompts/index/community\_report.py](/graphrag/prompts/index/community_report.py) | Python | 109 | 8 | 37 | 154 |
| [graphrag/prompts/index/community\_report\_text\_units.py](/graphrag/prompts/index/community_report_text_units.py) | Python | 68 | 7 | 21 | 96 |
| [graphrag/prompts/index/extract\_claims.py](/graphrag/prompts/index/extract_claims.py) | Python | 45 | 2 | 15 | 62 |
| [graphrag/prompts/index/extract\_graph.py](/graphrag/prompts/index/extract_graph.py) | Python | 103 | 12 | 17 | 132 |
| [graphrag/prompts/index/summarize\_descriptions.py](/graphrag/prompts/index/summarize_descriptions.py) | Python | 13 | 4 | 4 | 21 |
| [graphrag/prompts/query/\_\_init\_\_.py](/graphrag/prompts/query/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/prompts/query/basic\_search\_system\_prompt.py](/graphrag/prompts/query/basic_search_system_prompt.py) | Python | 34 | 2 | 38 | 74 |
| [graphrag/prompts/query/drift\_search\_system\_prompt.py](/graphrag/prompts/query/drift_search_system_prompt.py) | Python | 84 | 2 | 82 | 168 |
| [graphrag/prompts/query/global\_search\_knowledge\_system\_prompt.py](/graphrag/prompts/query/global_search_knowledge_system_prompt.py) | Python | 5 | 2 | 3 | 10 |
| [graphrag/prompts/query/global\_search\_map\_system\_prompt.py](/graphrag/prompts/query/global_search_map_system_prompt.py) | Python | 53 | 2 | 31 | 86 |
| [graphrag/prompts/query/global\_search\_reduce\_system\_prompt.py](/graphrag/prompts/query/global_search_reduce_system_prompt.py) | Python | 42 | 2 | 42 | 86 |
| [graphrag/prompts/query/local\_search\_system\_prompt.py](/graphrag/prompts/query/local_search_system_prompt.py) | Python | 32 | 2 | 36 | 70 |
| [graphrag/prompts/query/question\_gen\_system\_prompt.py](/graphrag/prompts/query/question_gen_system_prompt.py) | Python | 13 | 2 | 14 | 29 |
| [graphrag/query/\_\_init\_\_.py](/graphrag/query/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/context\_builder/\_\_init\_\_.py](/graphrag/query/context_builder/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/context\_builder/builders.py](/graphrag/query/context_builder/builders.py) | Python | 54 | 2 | 20 | 76 |
| [graphrag/query/context\_builder/community\_context.py](/graphrag/query/context_builder/community_context.py) | Python | 212 | 12 | 40 | 264 |
| [graphrag/query/context\_builder/conversation\_history.py](/graphrag/query/context_builder/conversation_history.py) | Python | 167 | 5 | 41 | 213 |
| [graphrag/query/context\_builder/dynamic\_community\_selection.py](/graphrag/query/context_builder/dynamic_community_selection.py) | Python | 143 | 8 | 22 | 173 |
| [graphrag/query/context\_builder/entity\_extraction.py](/graphrag/query/context_builder/entity_extraction.py) | Python | 101 | 6 | 15 | 122 |
| [graphrag/query/context\_builder/local\_context.py](/graphrag/query/context_builder/local_context.py) | Python | 300 | 14 | 40 | 354 |
| [graphrag/query/context\_builder/rate\_prompt.py](/graphrag/query/context_builder/rate_prompt.py) | Python | 19 | 2 | 3 | 24 |
| [graphrag/query/context\_builder/rate\_relevancy.py](/graphrag/query/context_builder/rate_relevancy.py) | Python | 64 | 5 | 9 | 78 |
| [graphrag/query/context\_builder/source\_context.py](/graphrag/query/context_builder/source_context.py) | Python | 73 | 7 | 20 | 100 |
| [graphrag/query/factory.py](/graphrag/query/factory.py) | Python | 264 | 4 | 37 | 305 |
| [graphrag/query/indexer\_adapters.py](/graphrag/query/indexer_adapters.py) | Python | 199 | 8 | 38 | 245 |
| [graphrag/query/input/\_\_init\_\_.py](/graphrag/query/input/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/input/loaders/\_\_init\_\_.py](/graphrag/query/input/loaders/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/input/loaders/dfs.py](/graphrag/query/input/loaders/dfs.py) | Python | 241 | 2 | 19 | 262 |
| [graphrag/query/input/loaders/utils.py](/graphrag/query/input/loaders/utils.py) | Python | 158 | 2 | 28 | 188 |
| [graphrag/query/input/retrieval/\_\_init\_\_.py](/graphrag/query/input/retrieval/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/input/retrieval/community\_reports.py](/graphrag/query/input/retrieval/community_reports.py) | Python | 62 | 3 | 11 | 76 |
| [graphrag/query/input/retrieval/covariates.py](/graphrag/query/input/retrieval/covariates.py) | Python | 40 | 3 | 11 | 54 |
| [graphrag/query/input/retrieval/entities.py](/graphrag/query/input/retrieval/entities.py) | Python | 82 | 2 | 19 | 103 |
| [graphrag/query/input/retrieval/relationships.py](/graphrag/query/input/retrieval/relationships.py) | Python | 115 | 4 | 21 | 140 |
| [graphrag/query/input/retrieval/text\_units.py](/graphrag/query/input/retrieval/text_units.py) | Python | 40 | 3 | 11 | 54 |
| [graphrag/query/llm/\_\_init\_\_.py](/graphrag/query/llm/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/llm/text\_utils.py](/graphrag/query/llm/text_utils.py) | Python | 81 | 9 | 22 | 112 |
| [graphrag/query/question\_gen/\_\_init\_\_.py](/graphrag/query/question_gen/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/question\_gen/base.py](/graphrag/query/question_gen/base.py) | Python | 51 | 2 | 13 | 66 |
| [graphrag/query/question\_gen/local\_gen.py](/graphrag/query/question_gen/local_gen.py) | Python | 186 | 6 | 22 | 214 |
| [graphrag/query/structured\_search/\_\_init\_\_.py](/graphrag/query/structured_search/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/structured\_search/base.py](/graphrag/query/structured_search/base.py) | Python | 72 | 5 | 15 | 92 |
| [graphrag/query/structured\_search/basic\_search/\_\_init\_\_.py](/graphrag/query/structured_search/basic_search/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/structured\_search/basic\_search/basic\_context.py](/graphrag/query/structured_search/basic_search/basic_context.py) | Python | 97 | 3 | 14 | 114 |
| [graphrag/query/structured\_search/basic\_search/search.py](/graphrag/query/structured_search/basic_search/search.py) | Python | 139 | 2 | 22 | 163 |
| [graphrag/query/structured\_search/drift\_search/\_\_init\_\_.py](/graphrag/query/structured_search/drift_search/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/structured\_search/drift\_search/action.py](/graphrag/query/structured_search/drift_search/action.py) | Python | 186 | 7 | 45 | 238 |
| [graphrag/query/structured\_search/drift\_search/drift\_context.py](/graphrag/query/structured_search/drift_search/drift_context.py) | Python | 188 | 5 | 34 | 227 |
| [graphrag/query/structured\_search/drift\_search/primer.py](/graphrag/query/structured_search/drift_search/primer.py) | Python | 162 | 2 | 38 | 202 |
| [graphrag/query/structured\_search/drift\_search/search.py](/graphrag/query/structured_search/drift_search/search.py) | Python | 371 | 11 | 67 | 449 |
| [graphrag/query/structured\_search/drift\_search/state.py](/graphrag/query/structured_search/drift_search/state.py) | Python | 117 | 6 | 28 | 151 |
| [graphrag/query/structured\_search/global\_search/\_\_init\_\_.py](/graphrag/query/structured_search/global_search/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/structured\_search/global\_search/community\_context.py](/graphrag/query/structured_search/global_search/community_context.py) | Python | 125 | 5 | 15 | 145 |
| [graphrag/query/structured\_search/global\_search/search.py](/graphrag/query/structured_search/global_search/search.py) | Python | 434 | 12 | 51 | 497 |
| [graphrag/query/structured\_search/local\_search/\_\_init\_\_.py](/graphrag/query/structured_search/local_search/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/query/structured\_search/local\_search/mixed\_context.py](/graphrag/query/structured_search/local_search/mixed_context.py) | Python | 428 | 20 | 44 | 492 |
| [graphrag/query/structured\_search/local\_search/search.py](/graphrag/query/structured_search/local_search/search.py) | Python | 143 | 2 | 21 | 166 |
| [graphrag/storage/\_\_init\_\_.py](/graphrag/storage/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/storage/blob\_pipeline\_storage.py](/graphrag/storage/blob_pipeline_storage.py) | Python | 338 | 7 | 49 | 394 |
| [graphrag/storage/cosmosdb\_pipeline\_storage.py](/graphrag/storage/cosmosdb_pipeline_storage.py) | Python | 327 | 12 | 39 | 378 |
| [graphrag/storage/factory.py](/graphrag/storage/factory.py) | Python | 40 | 2 | 13 | 55 |
| [graphrag/storage/file\_pipeline\_storage.py](/graphrag/storage/file_pipeline_storage.py) | Python | 147 | 4 | 33 | 184 |
| [graphrag/storage/memory\_pipeline\_storage.py](/graphrag/storage/memory_pipeline_storage.py) | Python | 55 | 2 | 22 | 79 |
| [graphrag/storage/pipeline\_storage.py](/graphrag/storage/pipeline_storage.py) | Python | 75 | 2 | 26 | 103 |
| [graphrag/utils/\_\_init\_\_.py](/graphrag/utils/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/utils/api.py](/graphrag/utils/api.py) | Python | 224 | 3 | 31 | 258 |
| [graphrag/utils/cli.py](/graphrag/utils/cli.py) | Python | 40 | 3 | 12 | 55 |
| [graphrag/utils/storage.py](/graphrag/utils/storage.py) | Python | 29 | 2 | 14 | 45 |
| [graphrag/vector\_stores/\_\_init\_\_.py](/graphrag/vector_stores/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [graphrag/vector\_stores/azure\_ai\_search.py](/graphrag/vector_stores/azure_ai_search.py) | Python | 172 | 11 | 24 | 207 |
| [graphrag/vector\_stores/base.py](/graphrag/vector_stores/base.py) | Python | 61 | 2 | 23 | 86 |
| [graphrag/vector\_stores/cosmosdb.py](/graphrag/vector_stores/cosmosdb.py) | Python | 217 | 16 | 39 | 272 |
| [graphrag/vector\_stores/factory.py](/graphrag/vector_stores/factory.py) | Python | 38 | 2 | 13 | 53 |
| [graphrag/vector\_stores/lancedb.py](/graphrag/vector_stores/lancedb.py) | Python | 131 | 6 | 17 | 154 |
| [install\_requirements.sh](/install_requirements.sh) | Shell Script | 24 | 4 | 7 | 35 |
| [mkdocs.yaml](/mkdocs.yaml) | YAML | 102 | 0 | 9 | 111 |
| [poetry.lock](/poetry.lock) | toml | 5,976 | 0 | 478 | 6,454 |
| [pyproject.toml](/pyproject.toml) | toml | 244 | 0 | 30 | 274 |
| [query\_entities.py](/query_entities.py) | Python | 129 | 11 | 21 | 161 |
| [quick\_export.py](/quick_export.py) | Python | 173 | 16 | 28 | 217 |
| [sample\_queries.sql](/sample_queries.sql) | MS SQL | 133 | 25 | 28 | 186 |
| [scripts/semver-check.sh](/scripts/semver-check.sh) | Shell Script | 8 | 1 | 2 | 11 |
| [scripts/spellcheck.sh](/scripts/spellcheck.sh) | Shell Script | 1 | 1 | 0 | 2 |
| [scripts/start-azurite.sh](/scripts/start-azurite.sh) | Shell Script | 1 | 1 | 0 | 2 |
| [test\_db\_connection.py](/test_db_connection.py) | Python | 54 | 5 | 15 | 74 |
| [test\_spatiotemporal\_extraction.py](/test_spatiotemporal_extraction.py) | Python | 65 | 13 | 15 | 93 |
| [tests/\_\_init\_\_.py](/tests/__init__.py) | Python | 8 | 3 | 5 | 16 |
| [tests/conftest.py](/tests/conftest.py) | Python | 4 | 2 | 3 | 9 |
| [tests/fixtures/azure/config.json](/tests/fixtures/azure/config.json) | JSON | 13 | 0 | 0 | 13 |
| [tests/fixtures/azure/input/ABOUT.md](/tests/fixtures/azure/input/ABOUT.md) | Markdown | 2 | 0 | 1 | 3 |
| [tests/fixtures/azure/settings.yml](/tests/fixtures/azure/settings.yml) | YAML | 30 | 0 | 7 | 37 |
| [tests/fixtures/min-csv/config.json](/tests/fixtures/min-csv/config.json) | JSON | 107 | 0 | 1 | 108 |
| [tests/fixtures/min-csv/input/ABOUT.md](/tests/fixtures/min-csv/input/ABOUT.md) | Markdown | 2 | 0 | 1 | 3 |
| [tests/fixtures/min-csv/settings.yml](/tests/fixtures/min-csv/settings.yml) | YAML | 40 | 0 | 4 | 44 |
| [tests/fixtures/text/config.json](/tests/fixtures/text/config.json) | JSON | 129 | 0 | 1 | 130 |
| [tests/fixtures/text/input/ABOUT.md](/tests/fixtures/text/input/ABOUT.md) | Markdown | 2 | 0 | 1 | 3 |
| [tests/fixtures/text/settings.yml](/tests/fixtures/text/settings.yml) | YAML | 44 | 0 | 6 | 50 |
| [tests/integration/\_\_init\_\_.py](/tests/integration/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/integration/language\_model/\_\_init\_\_.py](/tests/integration/language_model/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/integration/language\_model/test\_factory.py](/tests/integration/language_model/test_factory.py) | Python | 71 | 2 | 25 | 98 |
| [tests/integration/storage/\_\_init\_\_.py](/tests/integration/storage/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/integration/storage/test\_blob\_pipeline\_storage.py](/tests/integration/storage/test_blob_pipeline_storage.py) | Python | 93 | 3 | 20 | 116 |
| [tests/integration/storage/test\_cosmosdb\_storage.py](/tests/integration/storage/test_cosmosdb_storage.py) | Python | 105 | 4 | 25 | 134 |
| [tests/integration/storage/test\_factory.py](/tests/integration/storage/test_factory.py) | Python | 53 | 4 | 19 | 76 |
| [tests/integration/storage/test\_file\_pipeline\_storage.py](/tests/integration/storage/test_file_pipeline_storage.py) | Python | 49 | 2 | 16 | 67 |
| [tests/integration/vector\_stores/\_\_init\_\_.py](/tests/integration/vector_stores/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [tests/integration/vector\_stores/test\_azure\_ai\_search.py](/tests/integration/vector_stores/test_azure_ai_search.py) | Python | 115 | 6 | 26 | 147 |
| [tests/integration/vector\_stores/test\_cosmosdb.py](/tests/integration/vector_stores/test_cosmosdb.py) | Python | 78 | 6 | 21 | 105 |
| [tests/integration/vector\_stores/test\_lancedb.py](/tests/integration/vector_stores/test_lancedb.py) | Python | 130 | 15 | 28 | 173 |
| [tests/mock\_provider.py](/tests/mock_provider.py) | Python | 100 | 2 | 24 | 126 |
| [tests/notebook/\_\_init\_\_.py](/tests/notebook/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/notebook/test\_notebooks.py](/tests/notebook/test_notebooks.py) | Python | 38 | 2 | 9 | 49 |
| [tests/smoke/\_\_init\_\_.py](/tests/smoke/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/smoke/test\_fixtures.py](/tests/smoke/test_fixtures.py) | Python | 216 | 15 | 46 | 277 |
| [tests/unit/\_\_init\_\_.py](/tests/unit/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/config/\_\_init\_\_.py](/tests/unit/config/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/config/fixtures/minimal\_config/settings.yaml](/tests/unit/config/fixtures/minimal_config/settings.yaml) | YAML | 9 | 0 | 0 | 9 |
| [tests/unit/config/fixtures/minimal\_config\_missing\_env\_var/settings.yaml](/tests/unit/config/fixtures/minimal_config_missing_env_var/settings.yaml) | YAML | 9 | 0 | 0 | 9 |
| [tests/unit/config/test\_config.py](/tests/unit/config/test_config.py) | Python | 142 | 5 | 37 | 184 |
| [tests/unit/config/utils.py](/tests/unit/config/utils.py) | Python | 367 | 2 | 65 | 434 |
| [tests/unit/indexing/\_\_init\_\_.py](/tests/unit/indexing/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/cache/\_\_init\_\_.py](/tests/unit/indexing/cache/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/cache/test\_file\_pipeline\_cache.py](/tests/unit/indexing/cache/test_file_pipeline_cache.py) | Python | 52 | 5 | 18 | 75 |
| [tests/unit/indexing/graph/\_\_init\_\_.py](/tests/unit/indexing/graph/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/graph/extractors/\_\_init\_\_.py](/tests/unit/indexing/graph/extractors/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/graph/extractors/community\_reports/\_\_init\_\_.py](/tests/unit/indexing/graph/extractors/community_reports/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/graph/extractors/community\_reports/test\_sort\_context.py](/tests/unit/indexing/graph/extractors/community_reports/test_sort_context.py) | Python | 210 | 2 | 8 | 220 |
| [tests/unit/indexing/graph/utils/\_\_init\_\_.py](/tests/unit/indexing/graph/utils/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/graph/utils/test\_stable\_lcc.py](/tests/unit/indexing/graph/utils/test_stable_lcc.py) | Python | 51 | 7 | 14 | 72 |
| [tests/unit/indexing/input/\_\_init\_\_.py](/tests/unit/indexing/input/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/input/data/multiple-jsons/input1.json](/tests/unit/indexing/input/data/multiple-jsons/input1.json) | JSON | 10 | 0 | 1 | 11 |
| [tests/unit/indexing/input/data/multiple-jsons/input2.json](/tests/unit/indexing/input/data/multiple-jsons/input2.json) | JSON | 4 | 0 | 0 | 4 |
| [tests/unit/indexing/input/data/one-json-multiple-objects/input.json](/tests/unit/indexing/input/data/one-json-multiple-objects/input.json) | JSON | 10 | 0 | 1 | 11 |
| [tests/unit/indexing/input/data/one-json-one-object/input.json](/tests/unit/indexing/input/data/one-json-one-object/input.json) | JSON | 4 | 0 | 0 | 4 |
| [tests/unit/indexing/input/test\_csv\_loader.py](/tests/unit/indexing/input/test_csv_loader.py) | Python | 55 | 2 | 10 | 67 |
| [tests/unit/indexing/input/test\_json\_loader.py](/tests/unit/indexing/input/test_json_loader.py) | Python | 68 | 2 | 12 | 82 |
| [tests/unit/indexing/input/test\_txt\_loader.py](/tests/unit/indexing/input/test_txt_loader.py) | Python | 41 | 3 | 8 | 52 |
| [tests/unit/indexing/operations/\_\_init\_\_.py](/tests/unit/indexing/operations/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/operations/chunk\_text/\_\_init\_\_.py](/tests/unit/indexing/operations/chunk_text/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/operations/chunk\_text/test\_chunk\_text.py](/tests/unit/indexing/operations/chunk_text/test_chunk_text.py) | Python | 133 | 2 | 46 | 181 |
| [tests/unit/indexing/operations/chunk\_text/test\_strategies.py](/tests/unit/indexing/operations/chunk_text/test_strategies.py) | Python | 86 | 13 | 29 | 128 |
| [tests/unit/indexing/test\_init\_content.py](/tests/unit/indexing/test_init_content.py) | Python | 20 | 2 | 10 | 32 |
| [tests/unit/indexing/text\_splitting/\_\_init\_\_.py](/tests/unit/indexing/text_splitting/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/text\_splitting/test\_text\_splitting.py](/tests/unit/indexing/text_splitting/test_text_splitting.py) | Python | 145 | 2 | 56 | 203 |
| [tests/unit/indexing/verbs/\_\_init\_\_.py](/tests/unit/indexing/verbs/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/verbs/entities/\_\_init\_\_.py](/tests/unit/indexing/verbs/entities/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/verbs/entities/extraction/\_\_init\_\_.py](/tests/unit/indexing/verbs/entities/extraction/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/verbs/entities/extraction/strategies/\_\_init\_\_.py](/tests/unit/indexing/verbs/entities/extraction/strategies/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/verbs/entities/extraction/strategies/graph\_intelligence/\_\_init\_\_.py](/tests/unit/indexing/verbs/entities/extraction/strategies/graph_intelligence/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/verbs/entities/extraction/strategies/graph\_intelligence/test\_gi\_entity\_extraction.py](/tests/unit/indexing/verbs/entities/extraction/strategies/graph_intelligence/test_gi_entity_extraction.py) | Python | 170 | 37 | 17 | 224 |
| [tests/unit/indexing/verbs/helpers/\_\_init\_\_.py](/tests/unit/indexing/verbs/helpers/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/indexing/verbs/helpers/mock\_llm.py](/tests/unit/indexing/verbs/helpers/mock_llm.py) | Python | 8 | 2 | 4 | 14 |
| [tests/unit/query/\_\_init\_\_.py](/tests/unit/query/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/query/context\_builder/\_\_init\_\_.py](/tests/unit/query/context_builder/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/query/context\_builder/test\_entity\_extraction.py](/tests/unit/query/context_builder/test_entity_extraction.py) | Python | 169 | 2 | 17 | 188 |
| [tests/unit/query/input/\_\_init\_\_.py](/tests/unit/query/input/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/query/input/retrieval/\_\_init\_\_.py](/tests/unit/query/input/retrieval/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/query/input/retrieval/test\_entities.py](/tests/unit/query/input/retrieval/test_entities.py) | Python | 153 | 2 | 13 | 168 |
| [tests/unit/utils/\_\_init\_\_.py](/tests/unit/utils/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/unit/utils/test\_embeddings.py](/tests/unit/utils/test_embeddings.py) | Python | 11 | 2 | 9 | 22 |
| [tests/verbs/\_\_init\_\_.py](/tests/verbs/__init__.py) | Python | 0 | 2 | 1 | 3 |
| [tests/verbs/test\_create\_base\_text\_units.py](/tests/verbs/test_create_base_text_units.py) | Python | 40 | 5 | 24 | 69 |
| [tests/verbs/test\_create\_communities.py](/tests/verbs/test_create_communities.py) | Python | 35 | 3 | 11 | 49 |
| [tests/verbs/test\_create\_community\_reports.py](/tests/verbs/test_create_community_reports.py) | Python | 63 | 4 | 15 | 82 |
| [tests/verbs/test\_create\_final\_documents.py](/tests/verbs/test_create_final_documents.py) | Python | 37 | 3 | 20 | 60 |
| [tests/verbs/test\_create\_final\_text\_units.py](/tests/verbs/test_create_final_text_units.py) | Python | 29 | 2 | 11 | 42 |
| [tests/verbs/test\_extract\_covariates.py](/tests/verbs/test_extract_covariates.py) | Python | 58 | 6 | 16 | 80 |
| [tests/verbs/test\_extract\_graph.py](/tests/verbs/test_extract_graph.py) | Python | 57 | 10 | 12 | 79 |
| [tests/verbs/test\_extract\_graph\_nlp.py](/tests/verbs/test_extract_graph_nlp.py) | Python | 23 | 4 | 9 | 36 |
| [tests/verbs/test\_finalize\_graph.py](/tests/verbs/test_finalize_graph.py) | Python | 59 | 5 | 23 | 87 |
| [tests/verbs/test\_generate\_text\_embeddings.py](/tests/verbs/test_generate_text_embeddings.py) | Python | 51 | 4 | 14 | 69 |
| [tests/verbs/test\_pipeline\_state.py](/tests/verbs/test_pipeline_state.py) | Python | 35 | 3 | 17 | 55 |
| [tests/verbs/test\_prune\_graph.py](/tests/verbs/test_prune_graph.py) | Python | 21 | 2 | 9 | 32 |
| [tests/verbs/util.py](/tests/verbs/util.py) | Python | 69 | 4 | 21 | 94 |
| [unified-search-app/.vsts-ci.yml](/unified-search-app/.vsts-ci.yml) | YAML | 36 | 0 | 5 | 41 |
| [unified-search-app/Dockerfile](/unified-search-app/Dockerfile) | Docker | 11 | 5 | 4 | 20 |
| [unified-search-app/README.md](/unified-search-app/README.md) | Markdown | 98 | 0 | 30 | 128 |
| [unified-search-app/app/\_\_init\_\_.py](/unified-search-app/app/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [unified-search-app/app/app\_logic.py](/unified-search-app/app/app_logic.py) | Python | 285 | 11 | 73 | 369 |
| [unified-search-app/app/data\_config.py](/unified-search-app/app/data_config.py) | Python | 9 | 14 | 11 | 34 |
| [unified-search-app/app/home\_page.py](/unified-search-app/app/home_page.py) | Python | 216 | 2 | 43 | 261 |
| [unified-search-app/app/knowledge\_loader/\_\_init\_\_.py](/unified-search-app/app/knowledge_loader/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [unified-search-app/app/knowledge\_loader/data\_prep.py](/unified-search-app/app/knowledge_loader/data_prep.py) | Python | 55 | 2 | 19 | 76 |
| [unified-search-app/app/knowledge\_loader/data\_sources/\_\_init\_\_.py](/unified-search-app/app/knowledge_loader/data_sources/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [unified-search-app/app/knowledge\_loader/data\_sources/blob\_source.py](/unified-search-app/app/knowledge_loader/data_sources/blob_source.py) | Python | 98 | 2 | 28 | 128 |
| [unified-search-app/app/knowledge\_loader/data\_sources/default.py](/unified-search-app/app/knowledge_loader/data_sources/default.py) | Python | 12 | 2 | 7 | 21 |
| [unified-search-app/app/knowledge\_loader/data\_sources/loader.py](/unified-search-app/app/knowledge_loader/data_sources/loader.py) | Python | 61 | 2 | 16 | 79 |
| [unified-search-app/app/knowledge\_loader/data\_sources/local\_source.py](/unified-search-app/app/knowledge_loader/data_sources/local_source.py) | Python | 53 | 3 | 17 | 73 |
| [unified-search-app/app/knowledge\_loader/data\_sources/typing.py](/unified-search-app/app/knowledge_loader/data_sources/typing.py) | Python | 50 | 4 | 22 | 76 |
| [unified-search-app/app/knowledge\_loader/model.py](/unified-search-app/app/knowledge_loader/model.py) | Python | 85 | 2 | 24 | 111 |
| [unified-search-app/app/rag/\_\_init\_\_.py](/unified-search-app/app/rag/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [unified-search-app/app/rag/typing.py](/unified-search-app/app/rag/typing.py) | Python | 16 | 3 | 10 | 29 |
| [unified-search-app/app/state/\_\_init\_\_.py](/unified-search-app/app/state/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [unified-search-app/app/state/query\_variable.py](/unified-search-app/app/state/query_variable.py) | Python | 33 | 2 | 11 | 46 |
| [unified-search-app/app/state/session\_variable.py](/unified-search-app/app/state/session_variable.py) | Python | 36 | 2 | 16 | 54 |
| [unified-search-app/app/state/session\_variables.py](/unified-search-app/app/state/session_variables.py) | Python | 33 | 2 | 8 | 43 |
| [unified-search-app/app/ui/\_\_init\_\_.py](/unified-search-app/app/ui/__init__.py) | Python | 1 | 2 | 2 | 5 |
| [unified-search-app/app/ui/full\_graph.py](/unified-search-app/app/ui/full_graph.py) | Python | 47 | 2 | 8 | 57 |
| [unified-search-app/app/ui/questions\_list.py](/unified-search-app/app/ui/questions_list.py) | Python | 17 | 2 | 5 | 24 |
| [unified-search-app/app/ui/report\_details.py](/unified-search-app/app/ui/report_details.py) | Python | 79 | 6 | 14 | 99 |
| [unified-search-app/app/ui/report\_list.py](/unified-search-app/app/ui/report_list.py) | Python | 19 | 2 | 5 | 26 |
| [unified-search-app/app/ui/search.py](/unified-search-app/app/ui/search.py) | Python | 230 | 5 | 50 | 285 |
| [unified-search-app/app/ui/sidebar.py](/unified-search-app/app/ui/sidebar.py) | Python | 76 | 2 | 20 | 98 |
| [unified-search-app/poetry.lock](/unified-search-app/poetry.lock) | toml | 4,319 | 0 | 344 | 4,663 |
| [unified-search-app/pyproject.toml](/unified-search-app/pyproject.toml) | toml | 32 | 0 | 6 | 38 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)