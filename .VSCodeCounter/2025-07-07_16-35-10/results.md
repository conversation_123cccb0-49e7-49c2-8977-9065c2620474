# Summary

Date : 2025-07-07 16:35:10

Directory /home/<USER>/debug/graphrag

Total : 576 files,  50357 codes, 1847 comments, 10025 blanks, all 62229 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Python | 448 | 29,386 | 1,770 | 6,936 | 38,092 |
| toml | 4 | 10,571 | 0 | 858 | 11,429 |
| JSON | 45 | 5,676 | 0 | 17 | 5,693 |
| Markdown | 47 | 3,397 | 13 | 2,009 | 5,419 |
| YAML | 23 | 990 | 23 | 129 | 1,142 |
| JavaScript | 2 | 134 | 4 | 31 | 169 |
| MS SQL | 1 | 133 | 25 | 28 | 186 |
| Shell Script | 4 | 34 | 7 | 9 | 50 |
| CSS | 1 | 25 | 0 | 4 | 29 |
| Docker | 1 | 11 | 5 | 4 | 20 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 576 | 50,357 | 1,847 | 10,025 | 62,229 |
| . (Files) | 21 | 7,829 | 90 | 911 | 8,830 |
| .github | 15 | 666 | 36 | 103 | 805 |
| .github (Files) | 2 | 23 | 18 | 12 | 53 |
| .github/ISSUE_TEMPLATE | 4 | 161 | 2 | 13 | 176 |
| .github/workflows | 9 | 482 | 16 | 78 | 576 |
| .semversioner | 28 | 1,216 | 0 | 3 | 1,219 |
| .semversioner (Files) | 26 | 1,208 | 0 | 1 | 1,209 |
| .semversioner/next-release | 2 | 8 | 0 | 2 | 10 |
| .venv | 1 | 119 | 4 | 28 | 151 |
| .venv/lib | 1 | 119 | 4 | 28 | 151 |
| .venv/lib/python3.12 | 1 | 119 | 4 | 28 | 151 |
| .venv/lib/python3.12/site-packages | 1 | 119 | 4 | 28 | 151 |
| .venv/lib/python3.12/site-packages/mkdocs_jupyter | 1 | 119 | 4 | 28 | 151 |
| .venv/lib/python3.12/site-packages/mkdocs_jupyter/templates | 1 | 119 | 4 | 28 | 151 |
| .venv/lib/python3.12/site-packages/mkdocs_jupyter/templates/mkdocs_html | 1 | 119 | 4 | 28 | 151 |
| .venv/lib/python3.12/site-packages/mkdocs_jupyter/templates/mkdocs_html/assets | 1 | 119 | 4 | 28 | 151 |
| docs | 42 | 5,150 | 0 | 1,755 | 6,905 |
| docs (Files) | 6 | 294 | 0 | 144 | 438 |
| docs/config | 5 | 558 | 0 | 198 | 756 |
| docs/data | 2 | 487 | 0 | 487 | 974 |
| docs/data/operation_dulce | 2 | 487 | 0 | 487 | 974 |
| docs/examples_notebooks | 10 | 2,932 | 0 | 496 | 3,428 |
| docs/examples_notebooks (Files) | 8 | 2,445 | 0 | 8 | 2,453 |
| docs/examples_notebooks/inputs | 2 | 487 | 0 | 488 | 975 |
| docs/examples_notebooks/inputs/operation dulce | 2 | 487 | 0 | 488 | 975 |
| docs/index | 7 | 541 | 0 | 244 | 785 |
| docs/prompt_tuning | 3 | 125 | 0 | 83 | 208 |
| docs/query | 7 | 173 | 0 | 96 | 269 |
| docs/query (Files) | 6 | 165 | 0 | 90 | 255 |
| docs/query/notebooks | 1 | 8 | 0 | 6 | 14 |
| docs/scripts | 1 | 15 | 0 | 3 | 18 |
| docs/stylesheets | 1 | 25 | 0 | 4 | 29 |
| examples_notebooks | 3 | 1,741 | 0 | 5 | 1,746 |
| examples_notebooks/community_contrib | 3 | 1,741 | 0 | 5 | 1,746 |
| examples_notebooks/community_contrib (Files) | 1 | 3 | 0 | 3 | 6 |
| examples_notebooks/community_contrib/neo4j | 1 | 1,215 | 0 | 1 | 1,216 |
| examples_notebooks/community_contrib/yfiles-jupyter-graphs | 1 | 523 | 0 | 1 | 524 |
| graphrag | 344 | 23,680 | 1,352 | 5,513 | 30,545 |
| graphrag (Files) | 2 | 4 | 4 | 6 | 14 |
| graphrag/api | 4 | 1,324 | 45 | 179 | 1,548 |
| graphrag/cache | 6 | 246 | 12 | 90 | 348 |
| graphrag/callbacks | 12 | 402 | 26 | 141 | 569 |
| graphrag/cli | 6 | 1,287 | 59 | 167 | 1,513 |
| graphrag/config | 36 | 2,505 | 113 | 606 | 3,224 |
| graphrag/config (Files) | 12 | 1,059 | 58 | 301 | 1,418 |
| graphrag/config/models | 24 | 1,446 | 55 | 305 | 1,806 |
| graphrag/data_model | 12 | 497 | 33 | 141 | 671 |
| graphrag/index | 134 | 7,352 | 517 | 1,823 | 9,692 |
| graphrag/index (Files) | 2 | 40 | 7 | 13 | 60 |
| graphrag/index/input | 6 | 211 | 15 | 65 | 291 |
| graphrag/index/operations | 71 | 4,106 | 293 | 960 | 5,359 |
| graphrag/index/operations (Files) | 11 | 329 | 34 | 102 | 465 |
| graphrag/index/operations/build_noun_graph | 11 | 678 | 38 | 129 | 845 |
| graphrag/index/operations/build_noun_graph (Files) | 2 | 108 | 7 | 24 | 139 |
| graphrag/index/operations/build_noun_graph/np_extractors | 9 | 570 | 31 | 105 | 706 |
| graphrag/index/operations/chunk_text | 5 | 194 | 20 | 62 | 276 |
| graphrag/index/operations/embed_graph | 4 | 74 | 13 | 26 | 113 |
| graphrag/index/operations/embed_text | 6 | 357 | 21 | 95 | 473 |
| graphrag/index/operations/embed_text (Files) | 2 | 179 | 7 | 46 | 232 |
| graphrag/index/operations/embed_text/strategies | 4 | 178 | 14 | 49 | 241 |
| graphrag/index/operations/extract_covariates | 4 | 361 | 14 | 70 | 445 |
| graphrag/index/operations/extract_graph | 5 | 494 | 27 | 105 | 626 |
| graphrag/index/operations/layout_graph | 5 | 257 | 25 | 62 | 344 |
| graphrag/index/operations/summarize_communities | 15 | 1,068 | 81 | 232 | 1,381 |
| graphrag/index/operations/summarize_communities (Files) | 8 | 385 | 22 | 99 | 506 |
| graphrag/index/operations/summarize_communities/graph_context | 3 | 387 | 41 | 79 | 507 |
| graphrag/index/operations/summarize_communities/text_unit_context | 4 | 296 | 18 | 54 | 368 |
| graphrag/index/operations/summarize_descriptions | 5 | 294 | 20 | 77 | 391 |
| graphrag/index/run | 3 | 176 | 10 | 39 | 225 |
| graphrag/index/text_splitting | 3 | 163 | 10 | 46 | 219 |
| graphrag/index/typing | 8 | 93 | 17 | 51 | 161 |
| graphrag/index/update | 5 | 279 | 45 | 82 | 406 |
| graphrag/index/utils | 12 | 508 | 39 | 164 | 711 |
| graphrag/index/workflows | 24 | 1,776 | 81 | 403 | 2,260 |
| graphrag/language_model | 18 | 1,002 | 39 | 287 | 1,328 |
| graphrag/language_model (Files) | 3 | 208 | 8 | 56 | 272 |
| graphrag/language_model/cache | 2 | 25 | 4 | 13 | 42 |
| graphrag/language_model/events | 2 | 13 | 4 | 8 | 25 |
| graphrag/language_model/protocol | 2 | 128 | 4 | 44 | 176 |
| graphrag/language_model/providers | 6 | 538 | 13 | 134 | 685 |
| graphrag/language_model/providers (Files) | 1 | 1 | 2 | 2 | 5 |
| graphrag/language_model/providers/fnllm | 5 | 537 | 11 | 132 | 680 |
| graphrag/language_model/response | 3 | 90 | 6 | 32 | 128 |
| graphrag/logger | 9 | 350 | 22 | 138 | 510 |
| graphrag/prompt_tune | 28 | 1,084 | 136 | 348 | 1,568 |
| graphrag/prompt_tune (Files) | 3 | 26 | 6 | 14 | 46 |
| graphrag/prompt_tune/generator | 11 | 344 | 27 | 117 | 488 |
| graphrag/prompt_tune/loader | 2 | 90 | 7 | 16 | 113 |
| graphrag/prompt_tune/prompt | 8 | 436 | 70 | 139 | 645 |
| graphrag/prompt_tune/template | 4 | 188 | 26 | 62 | 276 |
| graphrag/prompts | 15 | 604 | 53 | 346 | 1,003 |
| graphrag/prompts (Files) | 1 | 1 | 2 | 2 | 5 |
| graphrag/prompts/index | 6 | 339 | 35 | 96 | 470 |
| graphrag/prompts/query | 8 | 264 | 16 | 248 | 528 |
| graphrag/query | 45 | 5,126 | 213 | 880 | 6,219 |
| graphrag/query (Files) | 3 | 464 | 14 | 77 | 555 |
| graphrag/query/context_builder | 10 | 1,134 | 63 | 212 | 1,409 |
| graphrag/query/input | 10 | 741 | 25 | 126 | 892 |
| graphrag/query/input (Files) | 1 | 1 | 2 | 2 | 5 |
| graphrag/query/input/loaders | 3 | 400 | 6 | 49 | 455 |
| graphrag/query/input/retrieval | 6 | 340 | 17 | 75 | 432 |
| graphrag/query/llm | 2 | 82 | 11 | 24 | 117 |
| graphrag/query/question_gen | 3 | 238 | 10 | 37 | 285 |
| graphrag/query/structured_search | 17 | 2,467 | 90 | 404 | 2,961 |
| graphrag/query/structured_search (Files) | 2 | 73 | 7 | 17 | 97 |
| graphrag/query/structured_search/basic_search | 3 | 237 | 7 | 38 | 282 |
| graphrag/query/structured_search/drift_search | 6 | 1,025 | 33 | 214 | 1,272 |
| graphrag/query/structured_search/global_search | 3 | 560 | 19 | 68 | 647 |
| graphrag/query/structured_search/local_search | 3 | 572 | 24 | 67 | 663 |
| graphrag/storage | 7 | 983 | 31 | 184 | 1,198 |
| graphrag/utils | 4 | 294 | 10 | 59 | 363 |
| graphrag/vector_stores | 6 | 620 | 39 | 118 | 777 |
| scripts | 3 | 10 | 3 | 2 | 15 |
| tests | 88 | 3,934 | 273 | 897 | 5,104 |
| tests (Files) | 3 | 112 | 7 | 32 | 151 |
| tests/fixtures | 9 | 369 | 0 | 22 | 391 |
| tests/fixtures/azure | 3 | 45 | 0 | 8 | 53 |
| tests/fixtures/azure (Files) | 2 | 43 | 0 | 7 | 50 |
| tests/fixtures/azure/input | 1 | 2 | 0 | 1 | 3 |
| tests/fixtures/min-csv | 3 | 149 | 0 | 6 | 155 |
| tests/fixtures/min-csv (Files) | 2 | 147 | 0 | 5 | 152 |
| tests/fixtures/min-csv/input | 1 | 2 | 0 | 1 | 3 |
| tests/fixtures/text | 3 | 175 | 0 | 8 | 183 |
| tests/fixtures/text (Files) | 2 | 173 | 0 | 7 | 180 |
| tests/fixtures/text/input | 1 | 2 | 0 | 1 | 3 |
| tests/integration | 12 | 695 | 50 | 185 | 930 |
| tests/integration (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/integration/language_model | 2 | 71 | 4 | 26 | 101 |
| tests/integration/storage | 5 | 300 | 15 | 81 | 396 |
| tests/integration/vector_stores | 4 | 324 | 29 | 77 | 430 |
| tests/notebook | 2 | 38 | 4 | 10 | 52 |
| tests/smoke | 2 | 216 | 17 | 47 | 280 |
| tests/unit | 46 | 1,927 | 138 | 398 | 2,463 |
| tests/unit (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/config | 5 | 527 | 9 | 103 | 639 |
| tests/unit/config (Files) | 3 | 509 | 9 | 103 | 621 |
| tests/unit/config/fixtures | 2 | 18 | 0 | 0 | 18 |
| tests/unit/config/fixtures/minimal_config | 1 | 9 | 0 | 0 | 9 |
| tests/unit/config/fixtures/minimal_config_missing_env_var | 1 | 9 | 0 | 0 | 9 |
| tests/unit/indexing | 32 | 1,067 | 111 | 250 | 1,428 |
| tests/unit/indexing (Files) | 2 | 20 | 4 | 11 | 35 |
| tests/unit/indexing/cache | 2 | 52 | 7 | 19 | 78 |
| tests/unit/indexing/graph | 6 | 261 | 17 | 26 | 304 |
| tests/unit/indexing/graph (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/indexing/graph/extractors | 3 | 210 | 6 | 10 | 226 |
| tests/unit/indexing/graph/extractors (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/indexing/graph/extractors/community_reports | 2 | 210 | 4 | 9 | 223 |
| tests/unit/indexing/graph/utils | 2 | 51 | 9 | 15 | 75 |
| tests/unit/indexing/input | 8 | 192 | 9 | 33 | 234 |
| tests/unit/indexing/input (Files) | 4 | 164 | 9 | 31 | 204 |
| tests/unit/indexing/input/data | 4 | 28 | 0 | 2 | 30 |
| tests/unit/indexing/input/data/multiple-jsons | 2 | 14 | 0 | 1 | 15 |
| tests/unit/indexing/input/data/one-json-multiple-objects | 1 | 10 | 0 | 1 | 11 |
| tests/unit/indexing/input/data/one-json-one-object | 1 | 4 | 0 | 0 | 4 |
| tests/unit/indexing/operations | 4 | 219 | 19 | 77 | 315 |
| tests/unit/indexing/operations (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/indexing/operations/chunk_text | 3 | 219 | 17 | 76 | 312 |
| tests/unit/indexing/text_splitting | 2 | 145 | 4 | 57 | 206 |
| tests/unit/indexing/verbs | 8 | 178 | 51 | 27 | 256 |
| tests/unit/indexing/verbs (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/indexing/verbs/entities | 5 | 170 | 45 | 21 | 236 |
| tests/unit/indexing/verbs/entities (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/indexing/verbs/entities/extraction | 4 | 170 | 43 | 20 | 233 |
| tests/unit/indexing/verbs/entities/extraction (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/indexing/verbs/entities/extraction/strategies | 3 | 170 | 41 | 19 | 230 |
| tests/unit/indexing/verbs/entities/extraction/strategies (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/indexing/verbs/entities/extraction/strategies/graph_intelligence | 2 | 170 | 39 | 18 | 227 |
| tests/unit/indexing/verbs/helpers | 2 | 8 | 4 | 5 | 17 |
| tests/unit/query | 6 | 322 | 12 | 34 | 368 |
| tests/unit/query (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/query/context_builder | 2 | 169 | 4 | 18 | 191 |
| tests/unit/query/input | 3 | 153 | 6 | 15 | 174 |
| tests/unit/query/input (Files) | 1 | 0 | 2 | 1 | 3 |
| tests/unit/query/input/retrieval | 2 | 153 | 4 | 14 | 171 |
| tests/unit/utils | 2 | 11 | 4 | 10 | 25 |
| tests/verbs | 14 | 577 | 57 | 203 | 837 |
| unified-search-app | 31 | 6,012 | 89 | 808 | 6,909 |
| unified-search-app (Files) | 5 | 4,496 | 5 | 389 | 4,890 |
| unified-search-app/app | 26 | 1,516 | 84 | 419 | 2,019 |
| unified-search-app/app (Files) | 4 | 511 | 29 | 129 | 669 |
| unified-search-app/app/knowledge_loader | 9 | 416 | 21 | 137 | 574 |
| unified-search-app/app/knowledge_loader (Files) | 3 | 141 | 6 | 45 | 192 |
| unified-search-app/app/knowledge_loader/data_sources | 6 | 275 | 15 | 92 | 382 |
| unified-search-app/app/rag | 2 | 17 | 5 | 12 | 34 |
| unified-search-app/app/state | 4 | 103 | 8 | 37 | 148 |
| unified-search-app/app/ui | 7 | 469 | 21 | 104 | 594 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)