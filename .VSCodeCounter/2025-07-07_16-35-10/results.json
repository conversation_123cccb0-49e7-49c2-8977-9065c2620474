{"file:///home/<USER>/debug/graphrag/install_requirements.sh": {"language": "<PERSON> Script", "code": 24, "comment": 4, "blank": 7}, "file:///home/<USER>/debug/graphrag/query_entities.py": {"language": "Python", "code": 129, "comment": 11, "blank": 21}, "file:///home/<USER>/debug/graphrag/test_db_connection.py": {"language": "Python", "code": 54, "comment": 5, "blank": 15}, "file:///home/<USER>/debug/graphrag/quick_export.py": {"language": "Python", "code": 173, "comment": 16, "blank": 28}, "file:///home/<USER>/debug/graphrag/test_spatiotemporal_extraction.py": {"language": "Python", "code": 65, "comment": 13, "blank": 15}, "file:///home/<USER>/debug/graphrag/sample_queries.sql": {"language": "MS SQL", "code": 133, "comment": 25, "blank": 28}, "file:///home/<USER>/debug/graphrag/unified-search-app/.vsts-ci.yml": {"language": "YAML", "code": 36, "comment": 0, "blank": 5}, "file:///home/<USER>/debug/graphrag/unified-search-app/Dockerfile": {"language": "<PERSON>er", "code": 11, "comment": 5, "blank": 4}, "file:///home/<USER>/debug/graphrag/unified-search-app/pyproject.toml": {"language": "toml", "code": 32, "comment": 0, "blank": 6}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/ui/report_details.py": {"language": "Python", "code": 79, "comment": 6, "blank": 14}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/ui/questions_list.py": {"language": "Python", "code": 17, "comment": 2, "blank": 5}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/ui/full_graph.py": {"language": "Python", "code": 47, "comment": 2, "blank": 8}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/ui/report_list.py": {"language": "Python", "code": 19, "comment": 2, "blank": 5}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/ui/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/ui/sidebar.py": {"language": "Python", "code": 76, "comment": 2, "blank": 20}, "file:///home/<USER>/debug/graphrag/export_entities_to_postgres.py": {"language": "Python", "code": 250, "comment": 16, "blank": 52}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/rag/typing.py": {"language": "Python", "code": 16, "comment": 3, "blank": 10}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/rag/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/model.py": {"language": "Python", "code": 85, "comment": 2, "blank": 24}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/state/session_variables.py": {"language": "Python", "code": 33, "comment": 2, "blank": 8}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/data_prep.py": {"language": "Python", "code": 55, "comment": 2, "blank": 19}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/state/session_variable.py": {"language": "Python", "code": 36, "comment": 2, "blank": 16}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/state/query_variable.py": {"language": "Python", "code": 33, "comment": 2, "blank": 11}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/ui/search.py": {"language": "Python", "code": 230, "comment": 5, "blank": 50}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/state/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/data_config.py": {"language": "Python", "code": 9, "comment": 14, "blank": 11}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/home_page.py": {"language": "Python", "code": 216, "comment": 2, "blank": 43}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/app_logic.py": {"language": "Python", "code": 285, "comment": 11, "blank": 73}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/data_sources/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/data_sources/loader.py": {"language": "Python", "code": 61, "comment": 2, "blank": 16}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/data_sources/typing.py": {"language": "Python", "code": 50, "comment": 4, "blank": 22}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/data_sources/local_source.py": {"language": "Python", "code": 53, "comment": 3, "blank": 17}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/data_sources/default.py": {"language": "Python", "code": 12, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/unified-search-app/app/knowledge_loader/data_sources/blob_source.py": {"language": "Python", "code": 98, "comment": 2, "blank": 28}, "file:///home/<USER>/debug/graphrag/unified-search-app/README.md": {"language": "<PERSON><PERSON>", "code": 98, "comment": 0, "blank": 30}, "file:///home/<USER>/debug/graphrag/pyproject.toml": {"language": "toml", "code": 244, "comment": 0, "blank": 30}, "file:///home/<USER>/debug/graphrag/mkdocs.yaml": {"language": "YAML", "code": 102, "comment": 0, "blank": 9}, "file:///home/<USER>/debug/graphrag/cspell.config.yaml": {"language": "YAML", "code": 31, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/breaking-changes.md": {"language": "<PERSON><PERSON>", "code": 57, "comment": 0, "blank": 36}, "file:///home/<USER>/debug/graphrag/SUPPORT.md": {"language": "<PERSON><PERSON>", "code": 16, "comment": 0, "blank": 12}, "file:///home/<USER>/debug/graphrag/SECURITY.md": {"language": "<PERSON><PERSON>", "code": 22, "comment": 0, "blank": 15}, "file:///home/<USER>/debug/graphrag/README.md": {"language": "<PERSON><PERSON>", "code": 53, "comment": 0, "blank": 25}, "file:///home/<USER>/debug/graphrag/RAI_TRANSPARENCY.md": {"language": "<PERSON><PERSON>", "code": 22, "comment": 0, "blank": 19}, "file:///home/<USER>/debug/graphrag/tests/__init__.py": {"language": "Python", "code": 8, "comment": 3, "blank": 5}, "file:///home/<USER>/debug/graphrag/DEVELOPING.md": {"language": "<PERSON><PERSON>", "code": 93, "comment": 0, "blank": 30}, "file:///home/<USER>/debug/graphrag/CONTRIBUTING.md": {"language": "<PERSON><PERSON>", "code": 52, "comment": 0, "blank": 28}, "file:///home/<USER>/debug/graphrag/CODE_OF_CONDUCT.md": {"language": "<PERSON><PERSON>", "code": 6, "comment": 0, "blank": 4}, "file:///home/<USER>/debug/graphrag/CHANGELOG.md": {"language": "<PERSON><PERSON>", "code": 291, "comment": 0, "blank": 53}, "file:///home/<USER>/debug/graphrag/scripts/semver-check.sh": {"language": "<PERSON> Script", "code": 8, "comment": 1, "blank": 2}, "file:///home/<USER>/debug/graphrag/.vsts-ci.yml": {"language": "YAML", "code": 36, "comment": 0, "blank": 5}, "file:///home/<USER>/debug/graphrag/docs/blog_posts.md": {"language": "<PERSON><PERSON>", "code": 30, "comment": 0, "blank": 24}, "file:///home/<USER>/debug/graphrag/graphrag/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/scripts/spellcheck.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/__main__.py": {"language": "Python", "code": 3, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/api/__init__.py": {"language": "Python", "code": 38, "comment": 5, "blank": 5}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/drift_search.ipynb": {"language": "JSON", "code": 234, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/api_overview.ipynb": {"language": "JSON", "code": 190, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/api/prompt_tune.py": {"language": "Python", "code": 169, "comment": 5, "blank": 25}, "file:///home/<USER>/debug/graphrag/docs/query/local_search.md": {"language": "<PERSON><PERSON>", "code": 45, "comment": 0, "blank": 18}, "file:///home/<USER>/debug/graphrag/graphrag/cache/factory.py": {"language": "Python", "code": 48, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/docs/query/multi_index_search.md": {"language": "<PERSON><PERSON>", "code": 10, "comment": 0, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/config/embeddings.py": {"language": "Python", "code": 38, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/.semversioner/0.3.5.json": {"language": "JSON", "code": 50, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/config/enums.py": {"language": "Python", "code": 113, "comment": 5, "blank": 51}, "file:///home/<USER>/debug/graphrag/docs/query/notebooks/overview.md": {"language": "<PERSON><PERSON>", "code": 8, "comment": 0, "blank": 6}, "file:///home/<USER>/debug/graphrag/.semversioner/0.4.0.json": {"language": "JSON", "code": 222, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/config/environment_reader.py": {"language": "Python", "code": 125, "comment": 2, "blank": 29}, "file:///home/<USER>/debug/graphrag/docs/index/default_dataflow.md": {"language": "<PERSON><PERSON>", "code": 152, "comment": 0, "blank": 60}, "file:///home/<USER>/debug/graphrag/docs/query/question_generation.md": {"language": "<PERSON><PERSON>", "code": 15, "comment": 0, "blank": 9}, "file:///home/<USER>/debug/graphrag/.semversioner/0.4.1.json": {"language": "JSON", "code": 34, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/docs/index/inputs.md": {"language": "<PERSON><PERSON>", "code": 176, "comment": 0, "blank": 94}, "file:///home/<USER>/debug/graphrag/graphrag/config/errors.py": {"language": "Python", "code": 39, "comment": 2, "blank": 19}, "file:///home/<USER>/debug/graphrag/docs/index/methods.md": {"language": "<PERSON><PERSON>", "code": 27, "comment": 0, "blank": 18}, "file:///home/<USER>/debug/graphrag/.semversioner/0.5.0.json": {"language": "JSON", "code": 38, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/config/get_embedding_settings.py": {"language": "Python", "code": 25, "comment": 9, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/index/update/relationships.py": {"language": "Python", "code": 59, "comment": 11, "blank": 16}, "file:///home/<USER>/debug/graphrag/.semversioner/0.3.6.json": {"language": "JSON", "code": 14, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/index/update/incremental_index.py": {"language": "Python", "code": 56, "comment": 6, "blank": 22}, "file:///home/<USER>/debug/graphrag/.semversioner/0.9.0.json": {"language": "JSON", "code": 46, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/config/load_config.py": {"language": "Python", "code": 154, "comment": 2, "blank": 36}, "file:///home/<USER>/debug/graphrag/.semversioner/1.0.1.json": {"language": "JSON", "code": 22, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/config/logging.py": {"language": "Python", "code": 48, "comment": 2, "blank": 12}, "file:///home/<USER>/debug/graphrag/docs/index/overview.md": {"language": "<PERSON><PERSON>", "code": 26, "comment": 0, "blank": 16}, "file:///home/<USER>/debug/graphrag/.semversioner/1.0.0.json": {"language": "JSON", "code": 26, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/docs/index/outputs.md": {"language": "<PERSON><PERSON>", "code": 93, "comment": 0, "blank": 17}, "file:///home/<USER>/debug/graphrag/graphrag/config/init_content.py": {"language": "Python", "code": 115, "comment": 26, "blank": 32}, "file:///home/<USER>/debug/graphrag/graphrag/config/read_dotenv.py": {"language": "Python", "code": 17, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/index/update/entities.py": {"language": "Python", "code": 54, "comment": 10, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/index/update/communities.py": {"language": "Python", "code": 109, "comment": 16, "blank": 27}, "file:///home/<USER>/debug/graphrag/graphrag/index/update/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/docs/query/overview.md": {"language": "<PERSON><PERSON>", "code": 21, "comment": 0, "blank": 17}, "file:///home/<USER>/debug/graphrag/graphrag/config/defaults.py": {"language": "Python", "code": 351, "comment": 2, "blank": 89}, "file:///home/<USER>/debug/graphrag/docs/prompt_tuning/overview.md": {"language": "<PERSON><PERSON>", "code": 8, "comment": 0, "blank": 8}, "file:///home/<USER>/debug/graphrag/.semversioner/0.3.4.json": {"language": "JSON", "code": 14, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/.semversioner/1.1.0.json": {"language": "JSON", "code": 58, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/config/create_graphrag_config.py": {"language": "Python", "code": 33, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/cache/pipeline_cache.py": {"language": "Python", "code": 47, "comment": 2, "blank": 19}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/.semversioner/1.1.1.json": {"language": "JSON", "code": 14, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/docs/prompt_tuning/manual_prompt_tuning.md": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 36}, "file:///home/<USER>/debug/graphrag/unified-search-app/poetry.lock": {"language": "toml", "code": 4319, "comment": 0, "blank": 344}, "file:///home/<USER>/debug/graphrag/graphrag/vector_stores/azure_ai_search.py": {"language": "Python", "code": 172, "comment": 11, "blank": 24}, "file:///home/<USER>/debug/graphrag/.semversioner/1.1.2.json": {"language": "JSON", "code": 10, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/utils/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/.semversioner/1.2.0.json": {"language": "JSON", "code": 26, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/storage/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/basic_search_config.py": {"language": "Python", "code": 25, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/vector_stores/base.py": {"language": "Python", "code": 61, "comment": 2, "blank": 23}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/cache_config.py": {"language": "Python", "code": 30, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/storage/blob_pipeline_storage.py": {"language": "Python", "code": 338, "comment": 7, "blank": 49}, "file:///home/<USER>/debug/graphrag/graphrag/vector_stores/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/utils/cli.py": {"language": "Python", "code": 40, "comment": 3, "blank": 12}, "file:///home/<USER>/debug/graphrag/graphrag/utils/api.py": {"language": "Python", "code": 224, "comment": 3, "blank": 31}, "file:///home/<USER>/debug/graphrag/docs/query/global_search.md": {"language": "<PERSON><PERSON>", "code": 52, "comment": 0, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/vector_stores/cosmosdb.py": {"language": "Python", "code": 217, "comment": 16, "blank": 39}, "file:///home/<USER>/debug/graphrag/graphrag/utils/storage.py": {"language": "Python", "code": 29, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/.semversioner/2.1.0.json": {"language": "JSON", "code": 22, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/.semversioner/2.0.0.json": {"language": "JSON", "code": 146, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/chunking_config.py": {"language": "Python", "code": 34, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/.semversioner/2.2.0.json": {"language": "JSON", "code": 46, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/storage/pipeline_storage.py": {"language": "Python", "code": 75, "comment": 2, "blank": 26}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/community_reports_config.py": {"language": "Python", "code": 54, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/vector_stores/lancedb.py": {"language": "Python", "code": 131, "comment": 6, "blank": 17}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/embed_graph_config.py": {"language": "Python", "code": 37, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/extract_claims_config.py": {"language": "Python", "code": 45, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/extract_graph_config.py": {"language": "Python", "code": 44, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/extract_graph_nlp_config.py": {"language": "Python", "code": 59, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/global_search_config.py": {"language": "Python", "code": 57, "comment": 3, "blank": 8}, "file:///home/<USER>/debug/graphrag/.semversioner/next-release/minor-20250519234123676262.json": {"language": "JSON", "code": 4, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/input_config.py": {"language": "Python", "code": 42, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/prune_graph_config.py": {"language": "Python", "code": 33, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/local_search_config.py": {"language": "Python", "code": 41, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/.semversioner/next-release/patch-20250530204951787463.json": {"language": "JSON", "code": 4, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/reporting_config.py": {"language": "Python", "code": 26, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/snapshots_config.py": {"language": "Python", "code": 17, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/graph_rag_config.py": {"language": "Python", "code": 304, "comment": 2, "blank": 58}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/drift_search_config.py": {"language": "Python", "code": 97, "comment": 2, "blank": 25}, "file:///home/<USER>/debug/graphrag/.semversioner/2.3.0.json": {"language": "JSON", "code": 34, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/storage/memory_pipeline_storage.py": {"language": "Python", "code": 55, "comment": 2, "blank": 22}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/language_model_config.py": {"language": "Python", "code": 277, "comment": 5, "blank": 47}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/storage_config.py": {"language": "Python", "code": 38, "comment": 5, "blank": 10}, "file:///home/<USER>/debug/graphrag/.semversioner/2.2.1.json": {"language": "JSON", "code": 18, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/summarize_descriptions_config.py": {"language": "Python", "code": 45, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/storage/file_pipeline_storage.py": {"language": "Python", "code": 147, "comment": 4, "blank": 33}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/cluster_graph_config.py": {"language": "Python", "code": 17, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/text_embedding_config.py": {"language": "Python", "code": 42, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/storage/factory.py": {"language": "Python", "code": 40, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/vector_stores/factory.py": {"language": "Python", "code": 38, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/index/validate_config.py": {"language": "Python", "code": 39, "comment": 5, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/storage/cosmosdb_pipeline_storage.py": {"language": "Python", "code": 327, "comment": 12, "blank": 39}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/umap_config.py": {"language": "Python", "code": 9, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/docs/index/byog.md": {"language": "<PERSON><PERSON>", "code": 41, "comment": 0, "blank": 30}, "file:///home/<USER>/debug/graphrag/graphrag/config/models/vector_store_config.py": {"language": "Python", "code": 72, "comment": 2, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/cache/noop_pipeline_cache.py": {"language": "Python", "code": 45, "comment": 2, "blank": 19}, "file:///home/<USER>/debug/graphrag/docs/index/architecture.md": {"language": "<PERSON><PERSON>", "code": 26, "comment": 0, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/config/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/docs/query/drift_search.md": {"language": "<PERSON><PERSON>", "code": 22, "comment": 0, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/__init__.py": {"language": "Python", "code": 92, "comment": 3, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/query/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/docs/prompt_tuning/auto_prompt_tuning.md": {"language": "<PERSON><PERSON>", "code": 63, "comment": 0, "blank": 39}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/dataframes.py": {"language": "Python", "code": 33, "comment": 2, "blank": 19}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/create_base_text_units.py": {"language": "Python", "code": 121, "comment": 3, "blank": 26}, "file:///home/<USER>/debug/graphrag/graphrag/index/typing/context.py": {"language": "Python", "code": 26, "comment": 3, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/derive_from_rows.py": {"language": "Python", "code": 119, "comment": 3, "blank": 40}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/create_community_reports.py": {"language": "Python", "code": 145, "comment": 8, "blank": 31}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/graphs.py": {"language": "Python", "code": 208, "comment": 2, "blank": 33}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/typing/pipeline_run_result.py": {"language": "Python", "code": 14, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/rate_limiter.py": {"language": "Python", "code": 27, "comment": 3, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/create_final_documents.py": {"language": "Python", "code": 53, "comment": 2, "blank": 17}, "file:///home/<USER>/debug/graphrag/graphrag/index/typing/state.py": {"language": "Python", "code": 3, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/rate_prompt.py": {"language": "Python", "code": 19, "comment": 2, "blank": 3}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/extract_graph.py": {"language": "Python", "code": 130, "comment": 4, "blank": 26}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/finalize_graph.py": {"language": "Python", "code": 58, "comment": 3, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/generate_text_embeddings.py": {"language": "Python", "code": 164, "comment": 2, "blank": 18}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/index/summarize_descriptions.py": {"language": "Python", "code": 13, "comment": 4, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/load_input_documents.py": {"language": "Python", "code": 31, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/index/extract_graph.py": {"language": "Python", "code": 103, "comment": 12, "blank": 17}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/loaders/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/load_update_documents.py": {"language": "Python", "code": 42, "comment": 4, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/loaders/utils.py": {"language": "Python", "code": 158, "comment": 2, "blank": 28}, "file:///home/<USER>/debug/graphrag/graphrag/query/question_gen/base.py": {"language": "Python", "code": 51, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/update_clean_state.py": {"language": "Python", "code": 20, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/prune_graph.py": {"language": "Python", "code": 59, "comment": 4, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/basic_search/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/query/question_gen/local_gen.py": {"language": "Python", "code": 186, "comment": 6, "blank": 22}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/update_communities.py": {"language": "Python", "code": 37, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/update_covariates.py": {"language": "Python", "code": 59, "comment": 4, "blank": 18}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/update_entities_relationships.py": {"language": "Python", "code": 82, "comment": 4, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/drift_search/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/drift_search/action.py": {"language": "Python", "code": 186, "comment": 7, "blank": 45}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/drift_search/drift_context.py": {"language": "Python", "code": 188, "comment": 5, "blank": 34}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/update_text_embeddings.py": {"language": "Python", "code": 47, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/update_text_units.py": {"language": "Python", "code": 68, "comment": 4, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/drift_search/search.py": {"language": "Python", "code": 371, "comment": 11, "blank": 67}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/drift_search/state.py": {"language": "Python", "code": 117, "comment": 6, "blank": 28}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/update_final_documents.py": {"language": "Python", "code": 22, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/basic_search/search.py": {"language": "Python", "code": 139, "comment": 2, "blank": 22}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/basic_search/basic_context.py": {"language": "Python", "code": 97, "comment": 3, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/drift_search/primer.py": {"language": "Python", "code": 162, "comment": 2, "blank": 38}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/update_community_reports.py": {"language": "Python", "code": 48, "comment": 2, "blank": 16}, "file:///home/<USER>/debug/graphrag/poetry.lock": {"language": "toml", "code": 5976, "comment": 0, "blank": 478}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/local_search/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/global_search/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/local_search/search.py": {"language": "Python", "code": 143, "comment": 2, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/local_search/mixed_context.py": {"language": "Python", "code": 428, "comment": 20, "blank": 44}, "file:///home/<USER>/debug/graphrag/graphrag/query/question_gen/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/global_search/community_context.py": {"language": "Python", "code": 125, "comment": 5, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/base.py": {"language": "Python", "code": 72, "comment": 5, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/loaders/dfs.py": {"language": "Python", "code": 241, "comment": 2, "blank": 19}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/index/extract_claims.py": {"language": "Python", "code": 45, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/factory.py": {"language": "Python", "code": 78, "comment": 3, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/index/typing/workflow.py": {"language": "Python", "code": 18, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/query/structured_search/global_search/search.py": {"language": "Python", "code": 434, "comment": 12, "blank": 51}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/retrieval/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/retrieval/community_reports.py": {"language": "Python", "code": 62, "comment": 3, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/retrieval/covariates.py": {"language": "Python", "code": 40, "comment": 3, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/retrieval/entities.py": {"language": "Python", "code": 82, "comment": 2, "blank": 19}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/index/community_report_text_units.py": {"language": "Python", "code": 68, "comment": 7, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/retrieval/relationships.py": {"language": "Python", "code": 115, "comment": 4, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/source_context.py": {"language": "Python", "code": 73, "comment": 7, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/query/input/retrieval/text_units.py": {"language": "Python", "code": 40, "comment": 3, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/uuid.py": {"language": "Python", "code": 8, "comment": 2, "blank": 5}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/extract_graph_nlp.py": {"language": "Python", "code": 50, "comment": 3, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/rate_relevancy.py": {"language": "Python", "code": 64, "comment": 5, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/typing/stats.py": {"language": "Python", "code": 15, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/tokens.py": {"language": "Python", "code": 32, "comment": 2, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/local_context.py": {"language": "Python", "code": 300, "comment": 14, "blank": 40}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/entity_extraction.py": {"language": "Python", "code": 101, "comment": 6, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/extract_covariates.py": {"language": "Python", "code": 70, "comment": 4, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/string.py": {"language": "Python", "code": 10, "comment": 4, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/dynamic_community_selection.py": {"language": "Python", "code": 143, "comment": 8, "blank": 22}, "file:///home/<USER>/debug/graphrag/graphrag/query/llm/text_utils.py": {"language": "Python", "code": 81, "comment": 9, "blank": 22}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/conversation_history.py": {"language": "Python", "code": 167, "comment": 5, "blank": 41}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/index/community_report.py": {"language": "Python", "code": 109, "comment": 8, "blank": 37}, "file:///home/<USER>/debug/graphrag/graphrag/query/llm/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/stable_lcc.py": {"language": "Python", "code": 36, "comment": 13, "blank": 19}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/community_context.py": {"language": "Python", "code": 212, "comment": 12, "blank": 40}, "file:///home/<USER>/debug/graphrag/graphrag/query/context_builder/builders.py": {"language": "Python", "code": 54, "comment": 2, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/is_null.py": {"language": "Python", "code": 10, "comment": 2, "blank": 8}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/create_final_text_units.py": {"language": "Python", "code": 93, "comment": 2, "blank": 27}, "file:///home/<USER>/debug/graphrag/graphrag/index/typing/pipeline.py": {"language": "Python", "code": 13, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/hashing.py": {"language": "Python", "code": 8, "comment": 2, "blank": 5}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/create_community_reports_text.py": {"language": "Python", "code": 85, "comment": 2, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/index/typing/error_handler.py": {"language": "Python", "code": 3, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/index/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/query/factory.py": {"language": "Python", "code": 264, "comment": 4, "blank": 37}, "file:///home/<USER>/debug/graphrag/graphrag/index/typing/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/utils/dicts.py": {"language": "Python", "code": 16, "comment": 2, "blank": 5}, "file:///home/<USER>/debug/graphrag/graphrag/query/indexer_adapters.py": {"language": "Python", "code": 199, "comment": 8, "blank": 38}, "file:///home/<USER>/debug/graphrag/.semversioner/0.3.3.json": {"language": "JSON", "code": 66, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/index/workflows/create_communities.py": {"language": "Python", "code": 122, "comment": 10, "blank": 20}, "file:///home/<USER>/debug/graphrag/.semversioner/0.3.2.json": {"language": "JSON", "code": 42, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/docs/visualization_guide.md": {"language": "<PERSON><PERSON>", "code": 78, "comment": 0, "blank": 23}, "file:///home/<USER>/debug/graphrag/graphrag/index/text_splitting/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/text_splitting/check_token_limit.py": {"language": "Python", "code": 9, "comment": 2, "blank": 5}, "file:///home/<USER>/debug/graphrag/graphrag/cache/memory_pipeline_cache.py": {"language": "Python", "code": 56, "comment": 2, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/query/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/run/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/text_splitting/text_splitting.py": {"language": "Python", "code": 153, "comment": 6, "blank": 39}, "file:///home/<USER>/debug/graphrag/docs/stylesheets/extra.css": {"language": "CSS", "code": 25, "comment": 0, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/create_graph.py": {"language": "Python", "code": 15, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/index/input/factory.py": {"language": "Python", "code": 46, "comment": 4, "blank": 12}, "file:///home/<USER>/debug/graphrag/graphrag/index/input/json.py": {"language": "Python", "code": 33, "comment": 3, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/logger/base.py": {"language": "Python", "code": 47, "comment": 2, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/finalize_relationships.py": {"language": "Python", "code": 33, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/graph_to_dataframes.py": {"language": "Python", "code": 24, "comment": 5, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/logger/rich_progress.py": {"language": "Python", "code": 129, "comment": 4, "blank": 33}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_covariates/claim_extractor.py": {"language": "Python", "code": 199, "comment": 8, "blank": 30}, "file:///home/<USER>/debug/graphrag/graphrag/logger/types.py": {"language": "Python", "code": 12, "comment": 3, "blank": 8}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_covariates/extract_covariates.py": {"language": "Python", "code": 125, "comment": 2, "blank": 26}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/query/question_gen_system_prompt.py": {"language": "Python", "code": 13, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/index/input/util.py": {"language": "Python", "code": 74, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/loader/input.py": {"language": "Python", "code": 89, "comment": 5, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/community_report_summarization.py": {"language": "Python", "code": 36, "comment": 3, "blank": 12}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/community_report_rating.py": {"language": "Python", "code": 25, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/community_reporter_role.py": {"language": "Python", "code": 25, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_descriptions/description_summary_extractor.py": {"language": "Python", "code": 99, "comment": 11, "blank": 24}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_descriptions/graph_intelligence_strategy.py": {"language": "Python", "code": 54, "comment": 3, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/types.py": {"language": "Python", "code": 11, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_descriptions/__init__.py": {"language": "Python", "code": 13, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/loader/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/domain.py": {"language": "Python", "code": 17, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_descriptions/summarize_descriptions.py": {"language": "Python", "code": 96, "comment": 2, "blank": 23}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/template/community_report_summarization.py": {"language": "Python", "code": 78, "comment": 7, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_descriptions/typing.py": {"language": "Python", "code": 32, "comment": 2, "blank": 17}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/entity_types.py": {"language": "Python", "code": 43, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/entity_summarization_prompt.py": {"language": "Python", "code": 26, "comment": 3, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/entity_relationship.py": {"language": "Python", "code": 50, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/extract_graph_prompt.py": {"language": "Python", "code": 86, "comment": 5, "blank": 19}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/language.py": {"language": "Python", "code": 17, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/template/extract_graph.py": {"language": "Python", "code": 95, "comment": 13, "blank": 34}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/template/entity_summarization.py": {"language": "Python", "code": 14, "comment": 4, "blank": 5}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/generator/persona.py": {"language": "Python", "code": 18, "comment": 2, "blank": 8}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/template/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_covariates/typing.py": {"language": "Python", "code": 36, "comment": 2, "blank": 12}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/snapshot_graphml.py": {"language": "Python", "code": 11, "comment": 2, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/query/local_search_system_prompt.py": {"language": "Python", "code": 32, "comment": 2, "blank": 36}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/prune_graph.py": {"language": "Python", "code": 71, "comment": 7, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/logger/progress.py": {"language": "Python", "code": 57, "comment": 2, "blank": 24}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/prompt/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/prompt/community_reporter_role.py": {"language": "Python", "code": 13, "comment": 2, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/prompt/community_report_rating.py": {"language": "Python", "code": 50, "comment": 28, "blank": 55}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/build_mixed_context.py": {"language": "Python", "code": 54, "comment": 6, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/base.py": {"language": "Python", "code": 46, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/prompt/domain.py": {"language": "Python", "code": 7, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/layout_graph/layout_graph.py": {"language": "Python", "code": 62, "comment": 5, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/community_reports_extractor.py": {"language": "Python", "code": 76, "comment": 3, "blank": 24}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/layout_graph/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/input/text.py": {"language": "Python", "code": 26, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_covariates/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/logger/print_progress.py": {"language": "Python", "code": 33, "comment": 2, "blank": 16}, "file:///home/<USER>/debug/graphrag/graphrag/logger/null_progress.py": {"language": "Python", "code": 23, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/prompt/entity_types.py": {"language": "Python", "code": 76, "comment": 2, "blank": 12}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/query/global_search_reduce_system_prompt.py": {"language": "Python", "code": 42, "comment": 2, "blank": 42}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/layout_graph/typing.py": {"language": "Python", "code": 15, "comment": 3, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/build_noun_graph.py": {"language": "Python", "code": 107, "comment": 5, "blank": 22}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/cfg_extractor.py": {"language": "Python", "code": 156, "comment": 5, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/explode_communities.py": {"language": "Python", "code": 16, "comment": 2, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/prompt/language.py": {"language": "Python", "code": 7, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/factory.py": {"language": "Python", "code": 70, "comment": 2, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/stop_words.py": {"language": "Python", "code": 17, "comment": 2, "blank": 3}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/summarize_communities.py": {"language": "Python", "code": 106, "comment": 2, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/resource_loader.py": {"language": "Python", "code": 28, "comment": 5, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/typing.py": {"language": "Python", "code": 44, "comment": 2, "blank": 18}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/graph_context/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/text_unit_context/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/text_unit_context/context_builder.py": {"language": "Python", "code": 193, "comment": 12, "blank": 27}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/prompt/entity_relationship.py": {"language": "Python", "code": 274, "comment": 30, "blank": 52}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/graph_context/sort_context.py": {"language": "Python", "code": 119, "comment": 13, "blank": 25}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/utils.py": {"language": "Python", "code": 10, "comment": 2, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/graph_context/context_builder.py": {"language": "Python", "code": 267, "comment": 26, "blank": 52}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/strategies.py": {"language": "Python", "code": 78, "comment": 3, "blank": 12}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/text_unit_context/prep_text_units.py": {"language": "Python", "code": 35, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/summarize_communities/text_unit_context/sort_context.py": {"language": "Python", "code": 67, "comment": 2, "blank": 16}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/layout_graph/zero.py": {"language": "Python", "code": 74, "comment": 7, "blank": 16}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/prompt/persona.py": {"language": "Python", "code": 8, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/regex_extractor.py": {"language": "Python", "code": 100, "comment": 6, "blank": 18}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/finalize_entities.py": {"language": "Python", "code": 52, "comment": 4, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/syntactic_parsing_extractor.py": {"language": "Python", "code": 136, "comment": 5, "blank": 22}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/layout_graph/umap.py": {"language": "Python", "code": 105, "comment": 8, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/np_extractors/np_validator.py": {"language": "Python", "code": 16, "comment": 2, "blank": 8}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/finalize_community_reports.py": {"language": "Python", "code": 22, "comment": 3, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/query/global_search_map_system_prompt.py": {"language": "Python", "code": 53, "comment": 2, "blank": 31}, "file:///home/<USER>/debug/graphrag/graphrag/logger/factory.py": {"language": "Python", "code": 32, "comment": 3, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/build_noun_graph/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/logger/console.py": {"language": "Python", "code": 16, "comment": 2, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/logger/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/query/global_search_knowledge_system_prompt.py": {"language": "Python", "code": 5, "comment": 2, "blank": 3}, "file:///home/<USER>/debug/graphrag/graphrag/prompt_tune/defaults.py": {"language": "Python", "code": 14, "comment": 2, "blank": 5}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_graph/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_graph/extract_graph.py": {"language": "Python", "code": 107, "comment": 8, "blank": 31}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/query/basic_search_system_prompt.py": {"language": "Python", "code": 34, "comment": 2, "blank": 38}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/compute_edge_combined_degree.py": {"language": "Python", "code": 32, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/prompts/query/drift_search_system_prompt.py": {"language": "Python", "code": 84, "comment": 2, "blank": 82}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_graph/graph_extractor.py": {"language": "Python", "code": 263, "comment": 12, "blank": 37}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_graph/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_graph/graph_intelligence_strategy.py": {"language": "Python", "code": 83, "comment": 3, "blank": 17}, "file:///home/<USER>/debug/graphrag/graphrag/index/input/csv.py": {"language": "Python", "code": 31, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/.venv/lib/python3.12/site-packages/mkdocs_jupyter/templates/mkdocs_html/assets/clipboard.umd.js": {"language": "JavaScript", "code": 119, "comment": 4, "blank": 28}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/compute_degree.py": {"language": "Python", "code": 9, "comment": 2, "blank": 5}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_graph/embed_graph.py": {"language": "Python", "code": 37, "comment": 4, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/index/run/utils.py": {"language": "Python", "code": 57, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_graph/typing.py": {"language": "Python", "code": 6, "comment": 3, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_graph/embed_node2vec.py": {"language": "Python", "code": 30, "comment": 4, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/extract_graph/typing.py": {"language": "Python", "code": 40, "comment": 2, "blank": 18}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/cluster_graph.py": {"language": "Python", "code": 59, "comment": 3, "blank": 19}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_text/embed_text.py": {"language": "Python", "code": 173, "comment": 5, "blank": 42}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_text/__init__.py": {"language": "Python", "code": 6, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/index/run/run_pipeline.py": {"language": "Python", "code": 118, "comment": 6, "blank": 27}, "file:///home/<USER>/debug/graphrag/graphrag/index/input/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/tests/notebook/test_notebooks.py": {"language": "Python", "code": 38, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/cache/json_pipeline_cache.py": {"language": "Python", "code": 49, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/chunk_text/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/.semversioner/0.3.1.json": {"language": "JSON", "code": 34, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/tests/smoke/test_fixtures.py": {"language": "Python", "code": 216, "comment": 15, "blank": 46}, "file:///home/<USER>/debug/graphrag/tests/notebook/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_text/strategies/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/chunk_text/bootstrap.py": {"language": "Python", "code": 21, "comment": 3, "blank": 8}, "file:///home/<USER>/debug/graphrag/docs/scripts/create_cookie_banner.js": {"language": "JavaScript", "code": 15, "comment": 0, "blank": 3}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_text/strategies/mock.py": {"language": "Python", "code": 24, "comment": 2, "blank": 8}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/chunk_text/chunk_text.py": {"language": "Python", "code": 103, "comment": 11, "blank": 27}, "file:///home/<USER>/debug/graphrag/graphrag/cache/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/multi_index_search.ipynb": {"language": "JSON", "code": 558, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/api/query.py": {"language": "Python", "code": 1042, "comment": 31, "blank": 128}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/chunk_text/strategies.py": {"language": "Python", "code": 53, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/tests/smoke/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/local_search.ipynb": {"language": "JSON", "code": 473, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_text/strategies/openai.py": {"language": "Python", "code": 135, "comment": 8, "blank": 30}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/chunk_text/typing.py": {"language": "Python", "code": 16, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/index/operations/embed_text/strategies/typing.py": {"language": "Python", "code": 18, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/community.py": {"language": "Python", "code": 61, "comment": 2, "blank": 17}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/inputs/operation%20dulce/ABOUT.md": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/document.py": {"language": "Python", "code": 37, "comment": 2, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/events/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/cache/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/entity.py": {"language": "Python", "code": 53, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/events/base.py": {"language": "Python", "code": 12, "comment": 2, "blank": 6}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/covariate.py": {"language": "Python", "code": 40, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/cache/base.py": {"language": "Python", "code": 24, "comment": 2, "blank": 11}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/identified.py": {"language": "Python", "code": 9, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/manager.py": {"language": "Python", "code": 118, "comment": 3, "blank": 31}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/schemas.py": {"language": "Python", "code": 138, "comment": 11, "blank": 21}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/named.py": {"language": "Python", "code": 8, "comment": 2, "blank": 7}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/factory.py": {"language": "Python", "code": 89, "comment": 3, "blank": 23}, "file:///home/<USER>/debug/graphrag/docs/index.md": {"language": "<PERSON><PERSON>", "code": 41, "comment": 0, "blank": 25}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/index_migration_to_v2.ipynb": {"language": "JSON", "code": 171, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/text_unit.py": {"language": "Python", "code": 47, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/.semversioner/0.3.0.json": {"language": "JSON", "code": 30, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/protocol/base.py": {"language": "Python", "code": 125, "comment": 2, "blank": 40}, "file:///home/<USER>/debug/graphrag/graphrag/cli/index.py": {"language": "Python", "code": 154, "comment": 5, "blank": 33}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/protocol/__init__.py": {"language": "Python", "code": 3, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/providers/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/inputs/operation%20dulce/Operation%20Dulce%20v2%201%201.md": {"language": "<PERSON><PERSON>", "code": 485, "comment": 0, "blank": 486}, "file:///home/<USER>/debug/graphrag/tests/verbs/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/response/base.py": {"language": "Python", "code": 50, "comment": 2, "blank": 20}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/response/base.pyi": {"language": "Python", "code": 39, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_create_community_reports.py": {"language": "Python", "code": 63, "comment": 4, "blank": 15}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_create_communities.py": {"language": "Python", "code": 35, "comment": 3, "blank": 11}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_create_final_documents.py": {"language": "Python", "code": 37, "comment": 3, "blank": 20}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_create_final_text_units.py": {"language": "Python", "code": 29, "comment": 2, "blank": 11}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_extract_graph_nlp.py": {"language": "Python", "code": 23, "comment": 4, "blank": 9}, "file:///home/<USER>/debug/graphrag/graphrag/cli/query.py": {"language": "Python", "code": 441, "comment": 35, "blank": 68}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_pipeline_state.py": {"language": "Python", "code": 35, "comment": 3, "blank": 17}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_generate_text_embeddings.py": {"language": "Python", "code": 51, "comment": 4, "blank": 14}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_finalize_graph.py": {"language": "Python", "code": 59, "comment": 5, "blank": 23}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_extract_graph.py": {"language": "Python", "code": 57, "comment": 10, "blank": 12}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_prune_graph.py": {"language": "Python", "code": 21, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/tests/verbs/util.py": {"language": "Python", "code": 69, "comment": 4, "blank": 21}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_extract_covariates.py": {"language": "Python", "code": 58, "comment": 6, "blank": 16}, "file:///home/<USER>/debug/graphrag/tests/verbs/test_create_base_text_units.py": {"language": "Python", "code": 40, "comment": 5, "blank": 24}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/response/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/cli/prompt_tune.py": {"language": "Python", "code": 105, "comment": 4, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/cli/main.py": {"language": "Python", "code": 507, "comment": 11, "blank": 37}, "file:///home/<USER>/debug/graphrag/graphrag/cli/initialize.py": {"language": "Python", "code": 79, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/graphrag/cli/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/types.py": {"language": "Python", "code": 3, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/global_search_with_dynamic_community_selection.ipynb": {"language": "JSON", "code": 295, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/providers/fnllm/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/index_migration_to_v1.ipynb": {"language": "JSON", "code": 261, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/providers/fnllm/cache.py": {"language": "Python", "code": 29, "comment": 2, "blank": 14}, "file:///home/<USER>/debug/graphrag/tests/unit/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/docs/examples_notebooks/global_search.ipynb": {"language": "JSON", "code": 263, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/providers/fnllm/events.py": {"language": "Python", "code": 16, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/config/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/providers/fnllm/utils.py": {"language": "Python", "code": 122, "comment": 3, "blank": 34}, "file:///home/<USER>/debug/graphrag/graphrag/language_model/providers/fnllm/models.py": {"language": "Python", "code": 369, "comment": 2, "blank": 73}, "file:///home/<USER>/debug/graphrag/tests/unit/utils/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/query/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/query/context_builder/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/cache/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/config/test_config.py": {"language": "Python", "code": 142, "comment": 5, "blank": 37}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/text_splitting/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/graph/utils/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/config/utils.py": {"language": "Python", "code": 367, "comment": 2, "blank": 65}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/text_splitting/test_text_splitting.py": {"language": "Python", "code": 145, "comment": 2, "blank": 56}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/input/test_json_loader.py": {"language": "Python", "code": 68, "comment": 2, "blank": 12}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/input/test_txt_loader.py": {"language": "Python", "code": 41, "comment": 3, "blank": 8}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/input/data/one-json-multiple-objects/input.json": {"language": "JSON", "code": 10, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/input/data/one-json-one-object/input.json": {"language": "JSON", "code": 4, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/graph/utils/test_stable_lcc.py": {"language": "Python", "code": 51, "comment": 7, "blank": 14}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/input/test_csv_loader.py": {"language": "Python", "code": 55, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/tests/unit/query/context_builder/test_entity_extraction.py": {"language": "Python", "code": 169, "comment": 2, "blank": 17}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/input/data/multiple-jsons/input1.json": {"language": "JSON", "code": 10, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/relationship.py": {"language": "Python", "code": 49, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/graphrag/data_model/community_report.py": {"language": "Python", "code": 51, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/input/data/multiple-jsons/input2.json": {"language": "JSON", "code": 4, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/input/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/verbs/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/test_init_content.py": {"language": "Python", "code": 20, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/graph/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/verbs/entities/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/cache/test_file_pipeline_cache.py": {"language": "Python", "code": 52, "comment": 5, "blank": 18}, "file:///home/<USER>/debug/graphrag/tests/unit/query/input/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/graph/extractors/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/utils/test_embeddings.py": {"language": "Python", "code": 11, "comment": 2, "blank": 9}, "file:///home/<USER>/debug/graphrag/.semversioner/0.2.2.json": {"language": "JSON", "code": 22, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/operations/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/verbs/helpers/mock_llm.py": {"language": "Python", "code": 8, "comment": 2, "blank": 4}, "file:///home/<USER>/debug/graphrag/tests/unit/query/input/retrieval/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/api/index.py": {"language": "Python", "code": 75, "comment": 4, "blank": 21}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/verbs/helpers/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/query/input/retrieval/test_entities.py": {"language": "Python", "code": 153, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/.semversioner/0.2.1.json": {"language": "JSON", "code": 70, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/graph/extractors/community_reports/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/operations/chunk_text/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/docs/data/operation_dulce/ABOUT.md": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/console_workflow_callbacks.py": {"language": "Python", "code": 21, "comment": 2, "blank": 10}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/file_workflow_callbacks.py": {"language": "Python", "code": 62, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/graph/extractors/community_reports/test_sort_context.py": {"language": "Python", "code": 210, "comment": 2, "blank": 8}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/llm_callbacks.py": {"language": "Python", "code": 7, "comment": 2, "blank": 6}, "file:///home/<USER>/debug/graphrag/tests/unit/config/fixtures/minimal_config/settings.yaml": {"language": "YAML", "code": 9, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/blob_workflow_callbacks.py": {"language": "Python", "code": 87, "comment": 4, "blank": 19}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/verbs/entities/extraction/strategies/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/operations/chunk_text/test_chunk_text.py": {"language": "Python", "code": 133, "comment": 2, "blank": 46}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/noop_query_callbacks.py": {"language": "Python", "code": 20, "comment": 2, "blank": 12}, "file:///home/<USER>/debug/graphrag/docs/data/operation_dulce/Operation%20Dulce%20v2%201%201.md": {"language": "<PERSON><PERSON>", "code": 485, "comment": 0, "blank": 486}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/verbs/entities/extraction/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/docs/get_started.md": {"language": "<PERSON><PERSON>", "code": 80, "comment": 0, "blank": 40}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/noop_workflow_callbacks.py": {"language": "Python", "code": 28, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/.semversioner/0.2.0.json": {"language": "JSON", "code": 94, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/examples_notebooks/community_contrib/yfiles-jupyter-graphs/graph-visualization.ipynb": {"language": "JSON", "code": 523, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/progress_workflow_callbacks.py": {"language": "Python", "code": 28, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/verbs/entities/extraction/strategies/graph_intelligence/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/docs/developing.md": {"language": "<PERSON><PERSON>", "code": 58, "comment": 0, "blank": 29}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/operations/chunk_text/test_strategies.py": {"language": "Python", "code": 86, "comment": 13, "blank": 29}, "file:///home/<USER>/debug/graphrag/tests/unit/indexing/verbs/entities/extraction/strategies/graph_intelligence/test_gi_entity_extraction.py": {"language": "Python", "code": 170, "comment": 37, "blank": 17}, "file:///home/<USER>/debug/graphrag/tests/mock_provider.py": {"language": "Python", "code": 100, "comment": 2, "blank": 24}, "file:///home/<USER>/debug/graphrag/.semversioner/0.1.0.json": {"language": "JSON", "code": 10, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/tests/unit/config/fixtures/minimal_config_missing_env_var/settings.yaml": {"language": "YAML", "code": 9, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/query_callbacks.py": {"language": "Python", "code": 20, "comment": 2, "blank": 12}, "file:///home/<USER>/debug/graphrag/examples_notebooks/community_contrib/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 3}, "file:///home/<USER>/debug/graphrag/examples_notebooks/community_contrib/neo4j/graphrag_import_neo4j_cypher.ipynb": {"language": "JSON", "code": 1215, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/reporting.py": {"language": "Python", "code": 30, "comment": 2, "blank": 8}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/workflow_callbacks.py": {"language": "Python", "code": 39, "comment": 2, "blank": 15}, "file:///home/<USER>/debug/graphrag/docs/cli.md": {"language": "<PERSON><PERSON>", "code": 7, "comment": 0, "blank": 3}, "file:///home/<USER>/debug/graphrag/graphrag/callbacks/workflow_callbacks_manager.py": {"language": "Python", "code": 59, "comment": 2, "blank": 16}, "file:///home/<USER>/debug/graphrag/tests/conftest.py": {"language": "Python", "code": 4, "comment": 2, "blank": 3}, "file:///home/<USER>/debug/graphrag/tests/integration/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/docs/config/env_vars.md": {"language": "<PERSON><PERSON>", "code": 173, "comment": 0, "blank": 47}, "file:///home/<USER>/debug/graphrag/docs/config/overview.md": {"language": "<PERSON><PERSON>", "code": 7, "comment": 0, "blank": 5}, "file:///home/<USER>/debug/graphrag/docs/config/yaml.md": {"language": "<PERSON><PERSON>", "code": 288, "comment": 0, "blank": 102}, "file:///home/<USER>/debug/graphrag/scripts/start-azurite.sh": {"language": "<PERSON> Script", "code": 1, "comment": 1, "blank": 0}, "file:///home/<USER>/debug/graphrag/tests/integration/vector_stores/test_azure_ai_search.py": {"language": "Python", "code": 115, "comment": 6, "blank": 26}, "file:///home/<USER>/debug/graphrag/tests/fixtures/text/config.json": {"language": "JSON", "code": 129, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/fixtures/min-csv/settings.yml": {"language": "YAML", "code": 40, "comment": 0, "blank": 4}, "file:///home/<USER>/debug/graphrag/tests/fixtures/text/input/ABOUT.md": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/fixtures/min-csv/config.json": {"language": "JSON", "code": 107, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/integration/vector_stores/test_cosmosdb.py": {"language": "Python", "code": 78, "comment": 6, "blank": 21}, "file:///home/<USER>/debug/graphrag/tests/integration/vector_stores/test_lancedb.py": {"language": "Python", "code": 130, "comment": 15, "blank": 28}, "file:///home/<USER>/debug/graphrag/tests/fixtures/text/settings.yml": {"language": "YAML", "code": 44, "comment": 0, "blank": 6}, "file:///home/<USER>/debug/graphrag/tests/integration/vector_stores/__init__.py": {"language": "Python", "code": 1, "comment": 2, "blank": 2}, "file:///home/<USER>/debug/graphrag/docs/config/models.md": {"language": "<PERSON><PERSON>", "code": 70, "comment": 0, "blank": 31}, "file:///home/<USER>/debug/graphrag/docs/config/init.md": {"language": "<PERSON><PERSON>", "code": 20, "comment": 0, "blank": 13}, "file:///home/<USER>/debug/graphrag/tests/fixtures/min-csv/input/ABOUT.md": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/.github/dependabot.yml": {"language": "YAML", "code": 10, "comment": 5, "blank": 1}, "file:///home/<USER>/debug/graphrag/tests/integration/storage/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}, "file:///home/<USER>/debug/graphrag/.github/ISSUE_TEMPLATE/config.yml": {"language": "YAML", "code": 1, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/.github/pull_request_template.md": {"language": "<PERSON><PERSON>", "code": 13, "comment": 13, "blank": 11}, "file:///home/<USER>/debug/graphrag/tests/integration/storage/test_file_pipeline_storage.py": {"language": "Python", "code": 49, "comment": 2, "blank": 16}, "file:///home/<USER>/debug/graphrag/tests/fixtures/azure/settings.yml": {"language": "YAML", "code": 30, "comment": 0, "blank": 7}, "file:///home/<USER>/debug/graphrag/tests/integration/storage/test_factory.py": {"language": "Python", "code": 53, "comment": 4, "blank": 19}, "file:///home/<USER>/debug/graphrag/tests/integration/storage/test_cosmosdb_storage.py": {"language": "Python", "code": 105, "comment": 4, "blank": 25}, "file:///home/<USER>/debug/graphrag/.github/ISSUE_TEMPLATE/feature_request.yml": {"language": "YAML", "code": 32, "comment": 0, "blank": 4}, "file:///home/<USER>/debug/graphrag/tests/integration/storage/test_blob_pipeline_storage.py": {"language": "Python", "code": 93, "comment": 3, "blank": 20}, "file:///home/<USER>/debug/graphrag/.github/ISSUE_TEMPLATE/general_issue.yml": {"language": "YAML", "code": 61, "comment": 1, "blank": 4}, "file:///home/<USER>/debug/graphrag/.github/workflows/gh-pages.yml": {"language": "YAML", "code": 44, "comment": 0, "blank": 10}, "file:///home/<USER>/debug/graphrag/.github/ISSUE_TEMPLATE/bug_report.yml": {"language": "YAML", "code": 67, "comment": 1, "blank": 4}, "file:///home/<USER>/debug/graphrag/tests/fixtures/azure/input/ABOUT.md": {"language": "<PERSON><PERSON>", "code": 2, "comment": 0, "blank": 1}, "file:///home/<USER>/debug/graphrag/.github/workflows/python-ci.yml": {"language": "YAML", "code": 78, "comment": 2, "blank": 13}, "file:///home/<USER>/debug/graphrag/tests/fixtures/azure/config.json": {"language": "JSON", "code": 13, "comment": 0, "blank": 0}, "file:///home/<USER>/debug/graphrag/.github/workflows/issues-autoresolve.yml": {"language": "YAML", "code": 27, "comment": 0, "blank": 3}, "file:///home/<USER>/debug/graphrag/.github/workflows/python-integration-tests.yml": {"language": "YAML", "code": 81, "comment": 6, "blank": 14}, "file:///home/<USER>/debug/graphrag/.github/workflows/python-publish.yml": {"language": "YAML", "code": 46, "comment": 0, "blank": 10}, "file:///home/<USER>/debug/graphrag/.github/workflows/python-smoke-tests.yml": {"language": "YAML", "code": 96, "comment": 4, "blank": 14}, "file:///home/<USER>/debug/graphrag/.github/workflows/python-notebook-tests.yml": {"language": "YAML", "code": 71, "comment": 2, "blank": 11}, "file:///home/<USER>/debug/graphrag/.github/workflows/semver.yml": {"language": "YAML", "code": 19, "comment": 1, "blank": 1}, "file:///home/<USER>/debug/graphrag/.github/workflows/spellcheck.yml": {"language": "YAML", "code": 20, "comment": 1, "blank": 2}, "file:///home/<USER>/debug/graphrag/tests/integration/language_model/test_factory.py": {"language": "Python", "code": 71, "comment": 2, "blank": 25}, "file:///home/<USER>/debug/graphrag/tests/integration/language_model/__init__.py": {"language": "Python", "code": 0, "comment": 2, "blank": 1}}