#!/usr/bin/env python3
"""
Test script to verify spatiotemporal information extraction in GraphRAG.
"""

import asyncio
import pandas as pd
from graphrag.index.operations.extract_graph.graph_extractor import GraphExtractor
from graphrag.index.operations.extract_graph.extract_graph import _merge_entities
from tests.mock_provider import MockChatLLM

# Mock LLM response with spatiotemporal information
MOCK_RESPONSE = '''
("entity"<|>APPLE INC<|>ORGANIZATION<|>Apple Inc. is a technology company that designs and manufactures consumer electronics<|>Founded in 1976<|>Cupertino, California)
##
("entity"<|>STEVE JOBS<|>PERSON<|><PERSON> was the co-founder and CEO of Apple Inc.<|>1955-2011<|>California, USA)
##
("entity"<|>IPHONE<|>PRODUCT<|>iPhone is a smartphone developed by Apple Inc.<|>First released in 2007<|>Designed in California)
##
("relationship"<|>STEVE JOBS<|>APPLE INC<|><PERSON> co-founded and led Apple Inc.<|>9)
##
("relationship"<|>APPLE INC<|>IPHONE<|>Apple Inc. developed and manufactures the iPhone<|>8)
<|COMPLETE|>
'''

async def test_spatiotemporal_extraction():
    """Test the spatiotemporal information extraction."""
    print("Testing spatiotemporal information extraction...")
    
    # Create a mock LLM that returns our test response
    mock_llm = MockChatLLM(responses=[MOCK_RESPONSE])
    
    # Create GraphExtractor with mock LLM
    extractor = GraphExtractor(
        model_invoker=mock_llm,
        max_gleanings=0
    )
    
    # Test text with temporal and spatial information
    test_text = """
    Apple Inc., founded in 1976 in Cupertino, California, is a technology company. 
    Steve Jobs, born in 1955 and died in 2011, was the co-founder and CEO. 
    The iPhone, first released in 2007, was designed in California.
    """
    
    # Extract entities and relationships
    result = await extractor([test_text], {
        "entity_types": "ORGANIZATION,PERSON,PRODUCT",
        "tuple_delimiter": "<|>",
        "record_delimiter": "##",
        "completion_delimiter": "<|COMPLETE|>"
    })
    
    print(f"Extracted {len(result.output.nodes)} entities and {len(result.output.edges)} relationships")
    
    # Check if temporal and spatial information was extracted
    for node_id, node_data in result.output.nodes(data=True):
        print(f"\nEntity: {node_id}")
        print(f"  Type: {node_data.get('type', 'N/A')}")
        print(f"  Description: {node_data.get('description', 'N/A')}")
        print(f"  Temporal Info: {node_data.get('temporal_info', 'N/A')}")
        print(f"  Spatial Info: {node_data.get('spatial_info', 'N/A')}")
    
    # Convert to DataFrame format for testing merge function
    entities_data = []
    for node_id, node_data in result.output.nodes(data=True):
        entities_data.append({
            'title': node_id,
            'type': node_data.get('type', ''),
            'description': node_data.get('description', ''),
            'source_id': node_data.get('source_id', ''),
            'temporal_info': node_data.get('temporal_info', 'UNKNOWN'),
            'spatial_info': node_data.get('spatial_info', 'UNKNOWN')
        })
    
    entities_df = pd.DataFrame(entities_data)
    print(f"\nEntities DataFrame:")
    print(entities_df[['title', 'type', 'temporal_info', 'spatial_info']])
    
    # Test merge function
    merged_entities = _merge_entities([entities_df])
    print(f"\nMerged entities:")
    print(merged_entities[['title', 'type', 'temporal_info', 'spatial_info']])
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_spatiotemporal_extraction())
    if success:
        print("\n✅ Spatiotemporal extraction test completed successfully!")
    else:
        print("\n❌ Spatiotemporal extraction test failed!")
