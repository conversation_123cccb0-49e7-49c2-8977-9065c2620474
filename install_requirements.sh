#!/bin/bash

echo "🚀 安装PostgreSQL Python依赖..."

# 检查是否在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 检测到虚拟环境: $VIRTUAL_ENV"
else
    echo "⚠️  建议在虚拟环境中运行"
fi

# 安装psycopg2
echo "📦 安装 psycopg2..."
pip install psycopg2-binary

# 验证安装
echo "🔍 验证安装..."
python -c "
try:
    import psycopg2
    print('✅ psycopg2 安装成功')
    print(f'   版本: {psycopg2.__version__}')
except ImportError as e:
    print(f'❌ psycopg2 安装失败: {e}')

try:
    import pandas as pd
    print('✅ pandas 可用')
    print(f'   版本: {pd.__version__}')
except ImportError as e:
    print(f'❌ pandas 不可用: {e}')
"

echo "✅ 依赖安装完成！"
