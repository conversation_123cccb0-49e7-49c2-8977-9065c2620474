extract_claims:
  enabled: true

vector_store:
  default_vector_store:
    type: "azure_ai_search"
    url: ${AZURE_AI_SEARCH_URL_ENDPOINT}
    api_key: ${AZURE_AI_SEARCH_API_KEY}
    container_name: "azure_ci"

input:
  storage:
    type: blob
    connection_string: ${LOCAL_BLOB_STORAGE_CONNECTION_STRING}
    container_name: azurefixture
    base_dir: input
  file_type: text
  

cache:
  type: blob
  connection_string: ${BLOB_STORAGE_CONNECTION_STRING}
  container_name: cicache
  base_dir: cache_azure_ai

output:
  type: blob
  connection_string: ${LOCAL_BLOB_STORAGE_CONNECTION_STRING}
  container_name: azurefixture
  base_dir: output

reporting:
  type: blob
  connection_string: ${LOCAL_BLOB_STORAGE_CONNECTION_STRING}
  container_name: azurefixture
  base_dir: reports
