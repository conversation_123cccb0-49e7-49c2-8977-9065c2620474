{"input_path": "./tests/fixtures/min-csv", "input_file_type": "text", "workflow_config": {"load_input_documents": {"max_runtime": 30}, "create_base_text_units": {"max_runtime": 30}, "extract_covariates": {"max_runtime": 10}, "extract_graph": {"max_runtime": 500}, "finalize_graph": {"row_range": [1, 500], "nan_allowed_columns": ["x", "y"], "max_runtime": 30, "expected_artifacts": ["entities.parquet", "relationships.parquet"]}, "create_communities": {"row_range": [10, 30], "max_runtime": 30, "expected_artifacts": ["communities.parquet"]}, "create_community_reports": {"row_range": [10, 30], "nan_allowed_columns": ["title", "summary", "full_content", "full_content_json", "rank", "rank_explanation", "findings", "period", "size"], "max_runtime": 300, "expected_artifacts": ["community_reports.parquet"]}, "create_final_text_units": {"row_range": [10, 50], "nan_allowed_columns": ["relationship_ids", "entity_ids", "covariate_ids"], "max_runtime": 30, "expected_artifacts": ["text_units.parquet"]}, "create_final_documents": {"row_range": [15, 15], "nan_allowed_columns": ["metadata"], "max_runtime": 30, "expected_artifacts": ["documents.parquet"]}, "generate_text_embeddings": {"row_range": [1, 500], "max_runtime": 150, "expected_artifacts": ["embeddings.text_unit.text.parquet", "embeddings.entity.description.parquet", "embeddings.community.full_content.parquet"]}}, "query_config": [{"query": "Who is Agent <PERSON> and what are his goals?", "method": "local"}, {"query": "What is the major conflict in this story and who are the protagonist and antagonist?", "method": "global"}], "slow": false}