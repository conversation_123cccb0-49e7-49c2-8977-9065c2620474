#!/usr/bin/env python3
"""
查询PostgreSQL中的GraphRAG实体数据
"""

import psycopg2
import pandas as pd
import tabulate

def query_entities():
    """查询实体数据"""
    
    # 数据库配置
    DB_CONFIG = {
        'host': 'localhost',
        'port': '5432',
        'database': 'graphrag',
        'user': 'postgres',
        'password': ''  # 使用默认的postgres用户，通常不需要密码
    }
    
    try:
        # 连接数据库
        print("📡 连接数据库...")
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            database=DB_CONFIG['database'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        cursor = conn.cursor()
        print("✅ 数据库连接成功")
        
        # 1. 基本统计
        print("\n📊 基本统计信息:")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_entities,
                COUNT(CASE WHEN temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN' THEN 1 END) as with_temporal,
                COUNT(CASE WHEN spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN' THEN 1 END) as with_spatial,
                COUNT(CASE WHEN temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN' 
                            AND spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN' THEN 1 END) as with_both
            FROM graphrag_entities;
        """)
        stats = cursor.fetchone()
        print(f"   总实体数: {stats[0]}")
        print(f"   有时间信息: {stats[1]}")
        print(f"   有空间信息: {stats[2]}")
        print(f"   有完整时空信息: {stats[3]}")
        
        # 2. 按类型统计
        print("\n📈 按类型分布:")
        cursor.execute("""
            SELECT type, COUNT(*) as count
            FROM graphrag_entities 
            WHERE type IS NOT NULL AND type != ''
            GROUP BY type 
            ORDER BY count DESC;
        """)
        type_stats = cursor.fetchall()
        for entity_type, count in type_stats:
            print(f"   {entity_type}: {count}")
        
        # 3. 有完整时空信息的实体
        print("\n🌍 有完整时空信息的实体:")
        cursor.execute("""
            SELECT title, type, temporal_info, spatial_info
            FROM graphrag_entities 
            WHERE temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN'
              AND spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN'
            ORDER BY title;
        """)
        spatiotemporal_entities = cursor.fetchall()
        
        if spatiotemporal_entities:
            headers = ["实体名称", "类型", "时间信息", "空间信息"]
            print(tabulate.tabulate(spatiotemporal_entities, headers=headers, tablefmt="grid"))
        else:
            print("   没有找到有完整时空信息的实体")
        
        # 4. 时间信息示例
        print("\n⏰ 时间信息示例:")
        cursor.execute("""
            SELECT title, type, temporal_info
            FROM graphrag_entities 
            WHERE temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN'
            ORDER BY title
            LIMIT 10;
        """)
        temporal_entities = cursor.fetchall()
        
        if temporal_entities:
            headers = ["实体名称", "类型", "时间信息"]
            print(tabulate.tabulate(temporal_entities, headers=headers, tablefmt="grid"))
        
        # 5. 空间信息示例
        print("\n🗺️  空间信息示例:")
        cursor.execute("""
            SELECT title, type, spatial_info
            FROM graphrag_entities 
            WHERE spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN'
            ORDER BY title
            LIMIT 10;
        """)
        spatial_entities = cursor.fetchall()
        
        if spatial_entities:
            headers = ["实体名称", "类型", "空间信息"]
            print(tabulate.tabulate(spatial_entities, headers=headers, tablefmt="grid"))
        
        # 6. 度数最高的实体（最重要的实体）
        print("\n🔗 连接度最高的实体:")
        cursor.execute("""
            SELECT title, type, degree, temporal_info, spatial_info
            FROM graphrag_entities 
            WHERE degree IS NOT NULL
            ORDER BY degree DESC
            LIMIT 5;
        """)
        top_entities = cursor.fetchall()
        
        if top_entities:
            headers = ["实体名称", "类型", "连接度", "时间信息", "空间信息"]
            print(tabulate.tabulate(top_entities, headers=headers, tablefmt="grid"))
        
        # 7. 搜索爱因斯坦相关实体
        print("\n🔍 爱因斯坦相关实体:")
        cursor.execute("""
            SELECT title, type, temporal_info, spatial_info, 
                   LEFT(description, 100) || '...' as description_preview
            FROM graphrag_entities 
            WHERE title ILIKE '%einstein%' 
               OR description ILIKE '%einstein%'
            ORDER BY title;
        """)
        einstein_entities = cursor.fetchall()
        
        if einstein_entities:
            headers = ["实体名称", "类型", "时间信息", "空间信息", "描述预览"]
            print(tabulate.tabulate(einstein_entities, headers=headers, tablefmt="grid"))
        
        cursor.close()
        conn.close()
        print("\n✅ 查询完成")
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

if __name__ == "__main__":
    # 尝试安装tabulate如果没有的话
    try:
        import tabulate
    except ImportError:
        print("📦 安装tabulate包...")
        import subprocess
        subprocess.check_call(["pip", "install", "tabulate"])
        import tabulate
    
    query_entities()
