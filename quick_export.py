#!/usr/bin/env python3
"""
快速导出GraphRAG实体到PostgreSQL的简化脚本
"""

import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import os
from datetime import datetime

def export_to_postgres():
    """快速导出函数"""
    
    # 数据库配置 - 请根据你的实际配置修改
    import getpass

    print("📋 请输入PostgreSQL连接信息:")
    host = input("主机地址 (默认: localhost): ").strip() or 'localhost'
    port = input("端口 (默认: 5432): ").strip() or '5432'
    database = input("数据库名 (默认: graphrag): ").strip() or 'graphrag'
    user = input("用户名 (默认: postgres): ").strip() or 'postgres'
    password = getpass.getpass("密码: ")

    DB_CONFIG = {
        'host': host,
        'port': port,
        'database': database,
        'user': user,
        'password': password
    }
    
    # 文件路径
    PARQUET_PATH = 'ragtest/output/entities.parquet'
    TABLE_NAME = 'graphrag_entities'
    
    print("🚀 开始导出GraphRAG实体到PostgreSQL...")
    
    # 检查文件是否存在
    if not os.path.exists(PARQUET_PATH):
        print(f"❌ 找不到文件: {PARQUET_PATH}")
        return
    
    conn = None
    cursor = None

    try:
        # 连接数据库
        print("📡 连接数据库...")
        conn = psycopg2.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            database=DB_CONFIG['database'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )
        cursor = conn.cursor()
        print("✅ 数据库连接成功")
        
        # 创建表
        print("🏗️  创建表...")
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
            id VARCHAR(255) PRIMARY KEY,
            human_readable_id INTEGER,
            title VARCHAR(500) NOT NULL,
            type VARCHAR(100),
            description TEXT,
            text_unit_ids TEXT[],
            frequency INTEGER,
            degree INTEGER,
            x FLOAT,
            y FLOAT,
            temporal_info TEXT,
            spatial_info TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_entities_title ON {TABLE_NAME}(title);
        CREATE INDEX IF NOT EXISTS idx_entities_type ON {TABLE_NAME}(type);
        CREATE INDEX IF NOT EXISTS idx_entities_temporal ON {TABLE_NAME}(temporal_info);
        CREATE INDEX IF NOT EXISTS idx_entities_spatial ON {TABLE_NAME}(spatial_info);
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        print("✅ 表创建成功")
        
        # 读取parquet文件
        print("📖 读取实体数据...")
        df = pd.read_parquet(PARQUET_PATH)
        print(f"📊 加载了 {len(df)} 个实体")
        
        # 准备数据
        print("🔄 准备数据...")
        data = []
        for _, row in df.iterrows():
            # 处理text_unit_ids
            text_unit_ids = row.get('text_unit_ids', [])
            if not isinstance(text_unit_ids, list):
                text_unit_ids = [str(text_unit_ids)] if text_unit_ids else []
            
            # 处理NaN值
            def safe_value(value):
                return None if pd.isna(value) else value
            
            record = (
                str(row['id']),
                int(row['human_readable_id']) if pd.notna(row['human_readable_id']) else None,
                str(row['title']),
                safe_value(row.get('type')),
                safe_value(row.get('description')),
                text_unit_ids,
                int(row['frequency']) if pd.notna(row.get('frequency')) else None,
                int(row['degree']) if pd.notna(row.get('degree')) else None,
                float(row['x']) if pd.notna(row.get('x')) else None,
                float(row['y']) if pd.notna(row.get('y')) else None,
                safe_value(row.get('temporal_info')),
                safe_value(row.get('spatial_info')),
                datetime.now()
            )
            data.append(record)
        
        # 插入数据
        print("💾 插入数据...")
        insert_sql = f"""
        INSERT INTO {TABLE_NAME} (
            id, human_readable_id, title, type, description, 
            text_unit_ids, frequency, degree, x, y, 
            temporal_info, spatial_info, created_at
        ) VALUES %s
        ON CONFLICT (id) DO UPDATE SET
            title = EXCLUDED.title,
            type = EXCLUDED.type,
            description = EXCLUDED.description,
            text_unit_ids = EXCLUDED.text_unit_ids,
            frequency = EXCLUDED.frequency,
            degree = EXCLUDED.degree,
            x = EXCLUDED.x,
            y = EXCLUDED.y,
            temporal_info = EXCLUDED.temporal_info,
            spatial_info = EXCLUDED.spatial_info;
        """
        
        execute_values(cursor, insert_sql, data, page_size=100)
        conn.commit()
        print("✅ 数据插入成功")
        
        # 显示统计信息
        print("\n📊 统计信息:")
        
        # 总数
        cursor.execute(f"SELECT COUNT(*) FROM {TABLE_NAME};")
        total = cursor.fetchone()[0]
        print(f"   总实体数: {total}")
        
        # 按类型统计
        cursor.execute(f"""
            SELECT type, COUNT(*) 
            FROM {TABLE_NAME} 
            WHERE type IS NOT NULL 
            GROUP BY type 
            ORDER BY COUNT(*) DESC;
        """)
        type_stats = cursor.fetchall()
        print("   按类型分布:")
        for entity_type, count in type_stats:
            print(f"     {entity_type}: {count}")
        
        # 时空信息统计
        cursor.execute(f"""
            SELECT 
                COUNT(CASE WHEN temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN' THEN 1 END) as with_temporal,
                COUNT(CASE WHEN spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN' THEN 1 END) as with_spatial
            FROM {TABLE_NAME};
        """)
        temporal_count, spatial_count = cursor.fetchone()
        print(f"   有时间信息: {temporal_count}")
        print(f"   有空间信息: {spatial_count}")
        
        # 显示一些示例
        print("\n🔍 时空信息示例:")
        cursor.execute(f"""
            SELECT title, temporal_info, spatial_info 
            FROM {TABLE_NAME} 
            WHERE (temporal_info IS NOT NULL AND temporal_info != 'UNKNOWN') 
               OR (spatial_info IS NOT NULL AND spatial_info != 'UNKNOWN')
            LIMIT 5;
        """)
        examples = cursor.fetchall()
        for title, temporal, spatial in examples:
            print(f"   {title}:")
            if temporal and temporal != 'UNKNOWN':
                print(f"     时间: {temporal}")
            if spatial and spatial != 'UNKNOWN':
                print(f"     空间: {spatial}")
        
        print(f"\n🎉 成功导出到PostgreSQL表: {TABLE_NAME}")
        print(f"💡 你可以使用以下SQL查询数据:")
        print(f"   SELECT * FROM {TABLE_NAME} LIMIT 10;")
        print(f"   SELECT title, temporal_info, spatial_info FROM {TABLE_NAME} WHERE temporal_info != 'UNKNOWN';")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        if conn is not None:
            conn.rollback()
    finally:
        if cursor is not None:
            cursor.close()
        if conn is not None:
            conn.close()
        print("🔌 数据库连接已关闭")

if __name__ == "__main__":
    export_to_postgres()
